[project]
name = "k8s-sop-agent"
version = "0.1.0"
description = "An AI agent for collaboratively writing expert-level Kubernetes operations SOPs."
authors = [{ name = "AI Agent", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    # --- Core Framework ---
    "crewai>=0.30.0,<1.0.0",
    "crewai[tools]>=0.1.0,<1.0.0",
    # --- RAG & Vector Store ---
    "fastembed>=0.2.0,<1.0.0",
    "qdrant-client>=1.7.0,<2.0.0", # Updated Qdrant integration for LangChain
    "langchain-qdrant>=0.1.0,<1.0.0", # Updated Qdrant integration for LangChain
    "chromadb>=0.4.24,<1.0.0", # A key dependency for crewai's internals
    # --- Core AI/LLM Libraries ---
    "langchain>=0.1.0,<1.0.0", # crewai is built on langchain
    "langchain-core>=0.1.0,<1.0.0", # Core LangChain components
    "langchain-community>=0.0.1,<1.0.0", # Community integrations
    "pydantic>=2.0.0,<3.0.0", # A critical dependency for almost everything
    # --- Utilities ---
    "python-dotenv>=1.0.0,<2.0.0",
    "markdown>=3.5.0,<4.0.0",
    "json-repair>=0.47.0,<1.0.0",
    "tenacity>=8.0.0,<9.0.0",
    "pybase64>=1.0.0,<2.0.0", # Often required by embedding/ML libraries
    "numpy>=1.21.0,<2.0.0", # Specify minimum version for Python 3.12 compatibility
    "httpx[socks]>=0.28.1,<1.0.0",
    "duckduckgo-search>=8.1.1,<9.0.0",
    "sentence-transformers>=5.0.0,<6.0.0",
    "transformers>=4.21.0,<5.0.0", # For cross-encoder rerank model
    "torch>=1.12.0,<3.0.0", # Required by transformers, allow 2.x versions
    "tokenizers>=0.13.0,<1.0.0", # Explicit tokenizers for transformers compatibility
    # --- Web API Dependencies ---
    "fastapi>=0.104.0,<1.0.0",
    "uvicorn[standard]>=0.24.0,<1.0.0",
    "python-multipart>=0.0.6,<1.0.0", # For file uploads
    "redis>=5.0.0,<6.0.0", # For session storage
    "python-jose[cryptography]>=3.3.0,<4.0.0", # For JWT tokens
    "prompt-toolkit>=3.0.51,<4.0.0", # Enhanced input handling with Chinese support
    # --- Python 3.12 Compatibility ---
    "typing-extensions>=4.8.0,<5.0.0", # Enhanced typing support for Python 3.12
    "setuptools>=65.0.0", # Required for some packages in Python 3.12
]

[tool.uv]
dev-dependencies = [
    "pytest",
]
