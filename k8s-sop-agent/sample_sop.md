# SOP: Deploy Nginx

This document describes how to deploy a simple Nginx server.

## Step 1: Deploy

Apply the deployment file to the cluster.

```
kubectl apply -f nginx-deployment.yaml
```

We also need a file called `nginx-deployment.yaml`.

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
```

## Step 2: Check Status

See if the pods are running.

```
kubectl get pods
```

That's it.