#!/usr/bin/env python3
"""
意图识别Crew - 基于CrewAI的智能意图分析系统

负责分析用户意图，包括：
1. 新需求检测
2. 跳过需求分析判断
3. 快速模式选择
4. 意图分类和路由
"""

from crewai import Agent, Task, Crew, Process
from crewai.tools import tool
from src.utils.llm_pool import llm_pool
import json
from typing import Dict, Any
import yaml
import os

class IntentCrew:
    """意图识别Crew - 专门处理用户意图分析"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.agents = self._create_agents()
        self.tasks = self._create_tasks()
    
    def _create_agents(self):
        """创建意图识别相关的Agent"""
        config = self._load_config()
        
        # 意图分析师 - 专门分析用户意图
        intent_analyzer = Agent(
            role=config['intent_analyzer_agent']['role'],
            goal=config['intent_analyzer_agent']['goal'],
            backstory=config['intent_analyzer_agent']['backstory'],
            verbose=config['intent_analyzer_agent']['verbose'],
            allow_delegation=False,
            llm=llm_pool.get_default_llm()
        )
        
        # 流程路由器 - 根据意图决定处理流程
        flow_router = Agent(
            role=config['flow_router_agent']['role'],
            goal=config['flow_router_agent']['goal'],
            backstory=config['flow_router_agent']['backstory'],
            verbose=config['flow_router_agent']['verbose'],
            allow_delegation=False,
            llm=llm_pool.get_default_llm()
        )
        
        return [intent_analyzer, flow_router]
    
    def _create_tasks(self):
        """创建意图识别相关的Task"""
        config = self._load_config()
        
        # 任务1：意图分析
        intent_analysis_task = Task(
            description=config['intent_analysis_task']['description'],
            expected_output=config['intent_analysis_task']['expected_output'],
            agent=self.agents[0]
        )
        
        # 任务2：流程决策
        flow_decision_task = Task(
            description=config['flow_decision_task']['description'],
            expected_output=config['flow_decision_task']['expected_output'],
            agent=self.agents[1],
            context=[intent_analysis_task]
        )
        
        return [intent_analysis_task, flow_decision_task]
    
    def crew(self) -> Crew:
        """创建并返回意图识别Crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,  # 禁用memory避免URL协议错误
            planning=False
        )
    
    def analyze_intent(self, question: str, current_requirement_info: Dict[str, Any], conversation_history: list) -> Dict[str, Any]:
        """
        分析用户意图的主要接口
        
        Args:
            question: 用户问题
            current_requirement_info: 当前需求信息
            conversation_history: 对话历史
            
        Returns:
            包含意图分析和流程决策的字典
        """
        try:
            # 准备输入数据
            inputs = {
                'question': question,
                'current_requirement_info': json.dumps(current_requirement_info, ensure_ascii=False, indent=2),
                'conversation_history': '\n'.join(conversation_history[-6:])  # 只取最近6轮对话
            }
            
            # 执行意图分析
            result = self.crew().kickoff(inputs=inputs)
            
            # 解析结果
            try:
                # 尝试解析JSON结果
                if hasattr(result, 'raw'):
                    result_text = str(result.raw)
                else:
                    result_text = str(result)
                
                # 提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    # 如果无法解析JSON，返回默认结果
                    return self._get_default_result()
                    
            except json.JSONDecodeError:
                print("⚠️ 无法解析意图分析结果，使用默认值")
                return self._get_default_result()
                
        except Exception as e:
            print(f"⚠️ 意图分析失败: {e}")
            return self._get_default_result()
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认的意图分析结果"""
        return {
            "is_new_requirement": False,
            "skip_requirements_analysis": False,
            "use_fast_mode": False,
            "reasoning": {
                "new_requirement": "意图分析失败，保守处理",
                "skip_analysis": "意图分析失败，进行完整分析",
                "fast_mode": "意图分析失败，使用完整模式"
            },
            "recommended_action": "进行标准的需求分析流程"
        }
    
    def _load_config(self):
        """加载配置文件"""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'intent_tasks.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

# 工具函数：快速意图检测
@tool
def quick_intent_check(question: str, context: str) -> str:
    """
    快速意图检测工具 - 用于简单场景的快速判断
    
    Args:
        question: 用户问题
        context: 上下文信息
        
    Returns:
        简单的意图分类结果
    """
    question_lower = question.lower()
    
    # 明确的新需求指示
    new_requirement_indicators = [
        "新的", "另一个", "接下来", "现在要", "换个", "不是刚才",
        "service", "deployment", "configmap", "secret", "ingress"
    ]
    
    # 调整请求指示
    adjustment_indicators = [
        "调整", "修改", "改进", "优化", "完善", "刚才", "之前", "上面"
    ]
    
    # 快速模式指示
    fast_mode_indicators = [
        "快速", "快点", "急", "紧急", "简单", "基础"
    ]
    
    # 跳过分析指示
    skip_indicators = [
        "直接生成", "不用问", "别问了", "立即生成"
    ]
    
    result = {
        "is_new_requirement": any(indicator in question_lower for indicator in new_requirement_indicators),
        "is_adjustment": any(indicator in question_lower for indicator in adjustment_indicators),
        "prefer_fast_mode": any(indicator in question_lower for indicator in fast_mode_indicators),
        "skip_analysis": any(indicator in question_lower for indicator in skip_indicators)
    }
    
    return json.dumps(result, ensure_ascii=False)
