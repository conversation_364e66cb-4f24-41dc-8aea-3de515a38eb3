"""
K8s SOP Agent Crews

This package contains specialized crews for different aspects of SOP creation:
- BaseSopCrew: Complete 3-agent workflow for full SOP generation
- RequirementsCrew: Specialized crew for requirements gathering
- FastSopCrew: Optimized 2-agent workflow for faster SOP generation
"""

from .base_sop_crew import BaseSop<PERSON>rew
from .requirements_crew import RequirementsCrew
from .fast_sop_crew import FastSopCrew

__all__ = [
    'BaseSopCrew',
    'RequirementsCrew',
    'FastSopCrew'
]