from crewai import Agent, Crew, Process, Task
from crewai.project import CrewBase, agent, crew, task
from typing import List

# Import your custom tool
from src.tools.kb_tool import KnowledgeBaseTool
from src.custom_embedder import get_embedder_config, setup_safe_environment
from src.utils.llm_pool import llm_pool

import os

@CrewBase
class BaseSopCrew:
    """Base SOP Crew专注于SOP文档生成 - 3-agent workflow (不包含需求收集)"""
    
    # Use absolute paths to ensure config files are found
    agents_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'agents.yaml')
    tasks_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'base_sop_tasks.yaml')

    def __init__(self, session_id: str = "default_session"):
        """
        Initialize the BaseSopCrew专注于SOP文档生成。
        
        注意: 此crew不包含需求收集功能，假设输入已包含完整的需求信息。
        需求收集应由RequirementsCrew单独处理。

        Args:
            session_id: A unique identifier for the session.
        """
        self.session_id = session_id
        
        # 设置CrewAI存储目录 - 实现会话隔离
        os.environ["CREWAI_STORAGE_DIR"] = f"./memory_storage/{session_id}"
        
        # Set up safe environment for embeddings
        setup_safe_environment()

        # Initialize any tools that need to be shared or have state
        # 使用单例模式的KnowledgeBaseTool，避免重复构建
        self.kb_tool = KnowledgeBaseTool()
        
        # 使用LLM连接池获取LLM实例
        self.llm = llm_pool.get_default_llm()



    @agent
    def query_rewriter_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['query_rewriter_agent'],
            llm=self.llm,
            memory=True,  # 使用CrewAI内置记忆
            respect_context_window=True,  # 自动处理上下文窗口
            verbose=True
        )

    @agent
    def knowledge_retriever_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['knowledge_retriever_agent'],
            tools=[self.kb_tool],
            llm=self.llm,
            memory=True,  # 使用CrewAI内置记忆
            respect_context_window=True,  # 自动处理上下文窗口
            verbose=True
        )

    @agent
    def response_synthesizer_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['response_synthesizer_agent'],
            llm=self.llm,
            memory=True,  # 使用CrewAI内置记忆
            respect_context_window=True,  # 自动处理上下文窗口
            verbose=True
        )



    @task
    def rewrite_query_task(self) -> Task:
        return Task(
            config=self.tasks_config['rewrite_query_task'],
            agent=self.query_rewriter_agent()
        )

    @task
    def retrieve_knowledge_task(self) -> Task:
        return Task(
            config=self.tasks_config['retrieve_knowledge_task'],
            agent=self.knowledge_retriever_agent()
        )

    @task
    def synthesize_response_task(self) -> Task:
        return Task(
            config=self.tasks_config['synthesize_response_task'],
            agent=self.response_synthesizer_agent()
        )

    @crew
    def crew(self) -> Crew:
        """Creates the BaseSopCrew with optimized configuration"""
        print(f"🧠 CrewAI会话存储: ./memory_storage/{self.session_id}")
        
        try:
            # 测试LLM连接
            print("🔗 测试LLM连接...")
            test_response = self.llm.call("Hello", callbacks=[])
            if not test_response or test_response.strip() == "":
                raise ValueError("LLM返回空响应，请检查API配置")
            print("✅ LLM连接正常")
            
            # 使用简化配置，避免Memory URL协议问题
            print("🔄 创建优化版Crew...")
            crew_instance = Crew(
                agents=self.agents,
                tasks=self.tasks,
                process=Process.sequential,
                verbose=True,
                memory=False,  # 暂时关闭memory避免URL协议错误
                respect_context_window=True,  # 保留上下文窗口管理
                planning=False
            )
            print("✅ 优化版Crew创建成功")
            return crew_instance
            
        except Exception as e:
            print(f"❌ Crew创建失败: {e}")
            raise RuntimeError(f"无法创建Crew实例: {e}")