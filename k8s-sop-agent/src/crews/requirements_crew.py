from crewai import Agent, Crew, Process, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
from typing import List

from src.custom_embedder import get_embedder_config, setup_safe_environment
from src.utils.llm_pool import llm_pool
import os

@CrewBase
class RequirementsCrew:
    """Requirements Collection Crew for SOP requirement gathering"""
    
    # Use absolute paths to ensure config files are found
    agents_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'agents.yaml')
    tasks_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'requirements_tasks.yaml')

    def __init__(self, session_id: str = "default_session"):
        """
        Initialize the RequirementsCrew with a specific session ID.

        Args:
            session_id: A unique identifier for the session.
        """
        self.session_id = session_id
        
        # Set up safe environment for embeddings
        setup_safe_environment()
        
        # 使用LLM连接池获取需求收集专用的LLM实例
        self.llm = llm_pool.get_requirements_llm()

    @agent
    def sop_requirements_advisor(self) -> Agent:
        """专业SOP编写指导者，负责收集和分析用户需求"""
        return Agent(
            config=self.agents_config['sop_requirements_advisor'],
            llm=self.llm,
            memory=True,
            verbose=True
        )

    @task
    def collect_sop_requirements_task(self) -> Task:
        """收集和分析SOP编写需求的任务"""
        return Task(
            config=self.tasks_config['collect_sop_requirements'],
            agent=self.sop_requirements_advisor()
        )

    @crew
    def crew(self) -> Crew:
        """Creates the requirements collection crew with only the requirements advisor"""
        # 确保memory存储目录存在
        memory_storage_path = os.path.abspath(f"./memory_storage/{self.session_id}")
        os.makedirs(memory_storage_path, exist_ok=True)
        
        print(f"🧠 Requirements Memory存储路径: {memory_storage_path}")
        
        try:
            # 使用简化的本地memory配置，避免依赖外部服务
            return Crew(
                agents=self.agents,
                tasks=self.tasks,
                process=Process.sequential,
                verbose=True,
                memory=True,
                respect_context_window=True,
                embedder=get_embedder_config(),
                planning=False
            )
        except Exception as e:
            print(f"❌ 创建Crew失败: {e}")
            # 如果仍然失败，则不使用memory
            return Crew(
                agents=self.agents,
                tasks=self.tasks,
                process=Process.sequential,
                verbose=True,
                memory=False,
                planning=False
            )