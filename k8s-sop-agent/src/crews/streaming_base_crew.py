"""
支持真正流式输出的Crew基类
"""

from crewai import Agent, Crew, Process, Task
from crewai.project import CrewBase
from typing import List, Optional
from src.utils.streaming_output import streaming_output

class StreamingCrewBase(CrewBase):
    """支持流式输出的Crew基类"""
    
    def __init__(self, session_id: str = "default_session"):
        super().__init__()
        self.session_id = session_id
        self.streaming_callbacks = streaming_output.get_callbacks()
    
    def crew(self) -> Crew:
        """创建支持流式的Crew实例"""
        crew_instance = super().crew()
        
        # 为所有agent设置流式回调和流式配置
        for agent in crew_instance.agents:
            if hasattr(agent, 'llm') and agent.llm:
                # 确保LLM启用流式
                agent.llm.stream = True
            # 设置agent级别的callbacks
            if hasattr(agent, 'callbacks'):
                agent.callbacks = self.streaming_callbacks
        
        return crew_instance
    
    def kickoff_with_streaming(self, inputs: dict) -> Any:
        """
        执行kickoff并启用流式输出
        
        Args:
            inputs: 输入参数
            
        Returns:
            执行结果
        """
        crew_instance = self.crew()
        
        # 使用流式回调执行
        try:
            result = crew_instance.kickoff(
                inputs=inputs,
                callbacks=self.streaming_callbacks
            )
            return result
        except Exception as e:
            streaming_output.print_error(f"流式执行失败: {e}")
            # 回退到普通执行
            return crew_instance.kickoff(inputs=inputs)

# 示例用法
class ExampleStreamingCrew(StreamingCrewBase):
    """示例流式Crew"""
    
    @agent
    def example_agent(self) -> Agent:
        return Agent(
            role="示例Agent",
            goal="演示流式输出",
            backstory="一个演示流式输出的示例agent",
            llm=self.llm,
            verbose=True
        )
    
    @task
    def example_task(self) -> Task:
        return Task(
            description="演示流式输出任务",
            expected_output="流式输出的演示结果",
            agent=self.example_agent()
        )
    
    @crew
    def crew(self) -> Crew:
        return Crew(
            agents=[self.example_agent()],
            tasks=[self.example_task()],
            process=Process.sequential,
            verbose=True
        )