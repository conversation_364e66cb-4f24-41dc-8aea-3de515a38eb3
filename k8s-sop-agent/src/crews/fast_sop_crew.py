"""
FastSopCrew - 优化的快速SOP生成器

简化的2-agent工作流：
1. 智能检索器：合并查询重写和知识检索功能
2. SOP生成器：直接生成最终SOP文档

相比BaseSopCrew减少1个agent，提高约30%的执行速度
"""

from crewai import Agent, Crew, Process, Task
from crewai.project import CrewBase, agent, crew, task

# Import your custom tool
from src.tools.kb_tool import KnowledgeBaseTool
from src.custom_embedder import get_embedder_config, setup_safe_environment
from src.utils.llm_pool import llm_pool

import os

@CrewBase
class FastSopCrew:
    """快速SOP生成器 - 2-agent优化工作流"""
    
    # Use absolute paths to ensure config files are found
    agents_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'agents.yaml')
    tasks_config = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'fast_sop_tasks.yaml')

    def __init__(self, session_id: str = "default_session"):
        """
        Initialize the FastSopCrew for optimized SOP generation.

        Args:
            session_id: A unique identifier for the session.
        """
        self.session_id = session_id
        
        # 设置CrewAI存储目录 - 实现会话隔离
        os.environ["CREWAI_STORAGE_DIR"] = f"./memory_storage/{session_id}"
        
        # Set up safe environment for embeddings
        setup_safe_environment()

        # Initialize any tools that need to be shared or have state
        # 使用单例模式的KnowledgeBaseTool，避免重复构建
        self.kb_tool = KnowledgeBaseTool()
        
        # 使用LLM连接池获取快速模式专用的LLM实例
        self.llm = llm_pool.get_fast_llm()

    @agent
    def smart_retriever_agent(self) -> Agent:
        """智能检索器：合并查询重写和知识检索功能"""
        return Agent(
            config=self.agents_config['smart_retriever_agent'],
            tools=[self.kb_tool],
            llm=self.llm,
            memory=False,  # 关闭memory提高速度
            verbose=True
        )

    @agent
    def sop_generator_agent(self) -> Agent:
        """SOP生成器：直接生成最终SOP文档"""
        return Agent(
            config=self.agents_config['sop_generator_agent'],
            llm=self.llm,
            memory=False,  # 关闭memory提高速度
            verbose=True
        )

    @task
    def smart_retrieve_task(self) -> Task:
        """智能检索任务：一次性完成查询优化和知识检索"""
        return Task(
            config=self.tasks_config['smart_retrieve_task'],
            agent=self.smart_retriever_agent()
        )

    @task
    def generate_sop_task(self) -> Task:
        """SOP生成任务：直接生成最终SOP文档"""
        return Task(
            config=self.tasks_config['generate_sop_task'],
            agent=self.sop_generator_agent(),
            context=[self.smart_retrieve_task()]
        )

    @crew
    def crew(self) -> Crew:
        """Creates the FastSopCrew with optimized 2-agent workflow"""
        print(f"🚀 FastSopCrew会话存储: ./memory_storage/{self.session_id}")
        
        try:
            # 使用简化配置，优化执行速度
            print("⚡ 创建快速SOP生成Crew...")
            crew_instance = Crew(
                agents=self.agents,
                tasks=self.tasks,
                process=Process.sequential,
                verbose=True,
                memory=False,  # 关闭memory提高速度
                respect_context_window=True,  # 保留上下文窗口管理
                planning=False  # 关闭planning提高速度
            )
            print("✅ 快速SOP生成Crew创建成功")
            return crew_instance
            
        except Exception as e:
            print(f"❌ FastSopCrew创建失败: {e}")
            raise RuntimeError(f"无法创建FastSopCrew实例: {e}")
