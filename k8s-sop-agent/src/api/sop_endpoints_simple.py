"""
简化版SOP建议API端点
使用新的crew架构，不依赖过时的session_manager
"""

import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

from src.models.api_models import SOPAdviceRequest, SOPAdviceResponse
from src.crews import BaseSopCrew, RequirementsCrew
from src.utils.cache_manager import cache_manager
from src.utils.streaming_output import stream_execute
from src.flows.sop_advice_flow import SopAdviceFlow

# 创建路由器
router = APIRouter(prefix="/api/v1", tags=["SOP建议"])


def _generate_request_id() -> str:
    """生成请求ID"""
    return str(uuid.uuid4())


def _is_asking_for_more_info(output: str) -> bool:
    """
    检测输出是否为询问更多信息的问题
    """
    import re
    
    # 检测强烈询问意图的关键词
    strong_asking_patterns = [
        r"请一次性回答以下问题",
        r"需要确认.*关键信息",
        r"缺少.*关键信息.*无法",
        r"必须.*提供.*才能",
        r"请告诉我.*具体的",
        r"无法.*推断.*需要",
    ]
    
    output_lower = output.lower()
    
    # 检查是否包含强烈询问模式
    for pattern in strong_asking_patterns:
        if re.search(pattern, output_lower):
            return True
    
    # 检查是否有连续的编号问题（3个或以上）
    numbered_questions = re.findall(r'\d+\.\s*[^。]*\?', output)
    if len(numbered_questions) >= 3:
        return True
    
    # 检查是否包含明确的信息缺失声明
    missing_info_patterns = [
        r"缺少.*信息",
        r"信息不足",
        r"需要.*更多.*信息",
    ]
    
    missing_info_count = 0
    for pattern in missing_info_patterns:
        if re.search(pattern, output_lower):
            missing_info_count += 1
    
    # 只有当同时满足多个条件时才认为是询问
    question_marks = output.count('?') + output.count('？')
    
    return missing_info_count >= 2 and question_marks >= 2


@router.post(
    "/sop-advice",
    response_model=SOPAdviceResponse,
    summary="获取SOP建议",
    description="基于用户问题提供K8s SOP建议，支持多轮对话"
)
async def get_sop_advice(
    request: SOPAdviceRequest
) -> SOPAdviceResponse:
    """
    获取SOP建议的主要端点（使用Flow模式）
    """
    request_id = _generate_request_id()
    
    try:
        # 使用Flow模式处理请求
        flow = SopAdviceFlow(session_id=request.session_id)
        
        # 设置输入数据
        flow.state.question = request.question
        flow.state.context = request.context
        
        # 执行流程
        flow.kickoff()
        
        # 获取响应结果
        result = flow.get_response()
        
        # 构建响应
        response = SOPAdviceResponse(
            session_id=request.session_id or flow.state.session_id,
            response=result["response"],
            is_question=result["is_question"],
            question_round=result["question_round"],
            max_rounds=result["max_rounds"],
            metadata={
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "question_status": result["status"],
                "context_provided": request.context is not None,
                "cache_hit": flow.state.metadata.get("cache_hit", False),
                "flow_mode": True
            }
        )
        
        return response
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"处理SOP请求时发生错误: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """健康检查端点"""
    cache_stats = cache_manager.stats()
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "K8s SOP Advisor API (Simplified)",
        "version": "1.0.0",
        "cache_stats": cache_stats
    }


@router.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    return cache_manager.stats()


@router.post("/cache/clear")
async def clear_cache():
    """清空缓存"""
    cache_manager.clear()
    return {"message": "缓存已清空", "timestamp": datetime.utcnow().isoformat()}