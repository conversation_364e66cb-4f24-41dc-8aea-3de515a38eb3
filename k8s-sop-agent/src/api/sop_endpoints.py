"""
SOP建议API端点实现
提供K8s SOP建议的RESTful API接口
"""

import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

from src.models.api_models import (
    SOPAdviceRequest, 
    SOPAdviceResponse, 
    ErrorResponse,
    APIResponse
)
# 注意: session_manager已被移除，使用新的crew架构
from src.crew import K8sSopCrew

# 创建路由器
router = APIRouter(prefix="/api/v1", tags=["SOP建议"])

# 全局会话管理器实例
session_manager: Optional[WebSessionManager] = None


async def get_session_manager() -> WebSessionManager:
    """
    获取会话管理器实例的依赖注入函数
    """
    global session_manager
    if session_manager is None:
        session_manager = WebSessionManager(
            storage_backend="memory",  # 可以通过环境变量配置
            session_timeout_hours=24,
            max_sessions_per_user=10
        )
        await session_manager.initialize()
    return session_manager


def _generate_request_id() -> str:
    """生成请求ID"""
    return str(uuid.uuid4())


def _is_asking_for_more_info(output: str) -> bool:
    """
    检测输出是否为询问更多信息的问题
    这个函数与crew.py中的逻辑保持一致
    """
    import re
    
    # 检测强烈询问意图的关键词
    strong_asking_patterns = [
        r"请一次性回答以下问题",
        r"需要确认.*关键信息",
        r"缺少.*关键信息.*无法",
        r"必须.*提供.*才能",
        r"请告诉我.*具体的",
        r"无法.*推断.*需要",
    ]
    
    output_lower = output.lower()
    
    # 检查是否包含强烈询问模式
    for pattern in strong_asking_patterns:
        if re.search(pattern, output_lower):
            return True
    
    # 检查是否有连续的编号问题（3个或以上）
    numbered_questions = re.findall(r'\d+\.\s*[^。]*\?', output)
    if len(numbered_questions) >= 3:
        return True
    
    # 检查是否包含明确的信息缺失声明
    missing_info_patterns = [
        r"缺少.*信息",
        r"信息不足",
        r"需要.*更多.*信息",
    ]
    
    missing_info_count = 0
    for pattern in missing_info_patterns:
        if re.search(pattern, output_lower):
            missing_info_count += 1
    
    # 只有当同时满足多个条件时才认为是询问
    question_marks = output.count('?') + output.count('？')
    
    return missing_info_count >= 2 and question_marks >= 2


async def _process_sop_request_async(
    sop_crew: K8sSopCrew, 
    question: str, 
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    异步处理SOP请求
    
    Args:
        sop_crew: K8sSopCrew实例
        question: 用户问题
        context: 额外上下文
        
    Returns:
        处理结果字典
    """
    try:
        # 准备输入数据
        inputs = {
            "question": question,  # 使用'question'而不是'user_query'以匹配任务配置
            "context": context or {}
        }
        
        # 在线程池中执行crew任务（因为crew.kickoff是同步的）
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, 
            lambda: sop_crew.crew().kickoff(inputs=inputs)
        )
        
        # 处理结果
        response_text = str(result.raw) if hasattr(result, 'raw') else str(result)
        is_question = _is_asking_for_more_info(response_text)
        
        return {
            "response": response_text,
            "is_question": is_question,
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"处理SOP请求时发生错误: {str(e)}"
        )


@router.post(
    "/sop-advice",
    response_model=SOPAdviceResponse,
    summary="获取SOP建议",
    description="基于用户问题提供K8s SOP建议，支持多轮对话"
)
async def get_sop_advice(
    request: SOPAdviceRequest,
    background_tasks: BackgroundTasks,
    session_mgr: WebSessionManager = Depends(get_session_manager)
) -> SOPAdviceResponse:
    """
    获取SOP建议的主要端点
    
    Args:
        request: SOP建议请求
        background_tasks: 后台任务
        session_mgr: 会话管理器
        
    Returns:
        SOP建议响应
    """
    request_id = _generate_request_id()
    
    try:
        # 处理会话ID
        session_id = request.session_id
        if session_id is None:
            # 创建新会话
            session_id = await session_mgr.create_session_async()
        else:
            # 验证会话是否存在
            try:
                session_data = await session_mgr.get_session_async(session_id)
                if session_data.get('status') != SessionStatus.ACTIVE:
                    raise HTTPException(
                        status_code=400,
                        detail=f"会话 {session_id} 不是活跃状态"
                    )
            except ValueError:
                raise HTTPException(
                    status_code=404,
                    detail=f"会话 {session_id} 不存在"
                )
        
        # 获取SOP Crew实例
        sop_crew = await session_mgr.get_sop_crew_async(session_id)
        
        # 异步处理SOP请求
        result = await _process_sop_request_async(
            sop_crew=sop_crew,
            question=request.question,
            context=request.context
        )
        
        # 构建响应
        response = SOPAdviceResponse(
            session_id=session_id,
            response=result["response"],
            is_question=result["is_question"],
            question_round=result["question_round"],
            max_rounds=result["max_rounds"],
            metadata={
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "question_status": result["question_status"],
                "context_provided": request.context is not None
            }
        )
        
        # 添加后台任务来更新会话活动时间
        background_tasks.add_task(
            session_mgr.update_session_activity,
            session_id
        )
        
        return response
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        raise HTTPException(
            status_code=500,
            detail=f"处理请求时发生内部错误: {str(e)}"
        )


@router.get(
    "/sop-advice/status/{session_id}",
    summary="获取会话状态",
    description="获取指定会话的当前状态信息"
)
async def get_session_status(
    session_id: str,
    session_mgr: WebSessionManager = Depends(get_session_manager)
) -> Dict[str, Any]:
    """
    获取会话状态信息
    
    Args:
        session_id: 会话ID
        session_mgr: 会话管理器
        
    Returns:
        会话状态信息
    """
    try:
        session_data = await session_mgr.get_session_async(session_id)
        
        # 获取SOP Crew实例来获取问题轮次信息
        try:
            sop_crew = await session_mgr.get_sop_crew_async(session_id)
            question_status = sop_crew.get_question_status()
            question_round = sop_crew.question_rounds
            max_rounds = sop_crew.max_question_rounds
            can_ask_more = sop_crew.can_ask_more_questions()
        except Exception:
            question_status = "未知"
            question_round = 0
            max_rounds = 2
            can_ask_more = True
        
        return {
            "session_id": session_id,
            "status": session_data.get('status', SessionStatus.ACTIVE),
            "created_at": session_data.get('created_at'),
            "last_activity": session_data.get('last_activity'),
            "message_count": session_data.get('message_count', 0),
            "question_round": question_round,
            "max_rounds": max_rounds,
            "can_ask_more_questions": can_ask_more,
            "question_status": question_status
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=404,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取会话状态时发生错误: {str(e)}"
        )


@router.post(
    "/sop-advice/reset/{session_id}",
    summary="重置会话",
    description="重置指定会话的记忆和问题轮次"
)
async def reset_session(
    session_id: str,
    session_mgr: WebSessionManager = Depends(get_session_manager)
) -> Dict[str, Any]:
    """
    重置会话记忆和状态
    
    Args:
        session_id: 会话ID
        session_mgr: 会话管理器
        
    Returns:
        重置结果
    """
    try:
        # 验证会话存在
        await session_mgr.get_session_async(session_id)
        
        # 重置会话记忆
        await session_mgr.reset_session_memory_async(session_id)
        
        # 重置SOP Crew的问题轮次
        try:
            sop_crew = await session_mgr.get_sop_crew_async(session_id)
            sop_crew.reset_session_question_rounds()
        except Exception as e:
            print(f"Warning: Could not reset crew question rounds: {e}")
        
        return {
            "session_id": session_id,
            "message": "会话已成功重置",
            "timestamp": datetime.utcnow().isoformat(),
            "reset_components": [
                "会话记忆",
                "问题轮次计数器",
                "消息计数"
            ]
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=404,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"重置会话时发生错误: {str(e)}"
        )


# 注意：错误处理器应该在主应用中注册，而不是在路由器中
# 这些函数可以在web_main.py中使用

def create_http_exception_handler():
    """创建HTTP异常处理器"""
    async def http_exception_handler(request, exc: HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": _generate_request_id()
                }
            }
        )
    return http_exception_handler


def create_general_exception_handler():
    """创建通用异常处理器"""
    async def general_exception_handler(request, exc: Exception):
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "服务器内部错误",
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": _generate_request_id()
                }
            }
        )
    return general_exception_handler