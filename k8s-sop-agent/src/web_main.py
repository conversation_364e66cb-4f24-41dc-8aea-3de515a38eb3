"""
FastAPI Web Application Entry Point for K8s SOP Advisor

This module provides the main FastAPI application instance with basic configuration,
CORS settings, and initial setup for the K8s SOP Advisor Web API.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import os
from dotenv import load_dotenv

# Import API routers and exception handlers
from src.api.sop_endpoints_simple import (
    router as sop_router,
    _generate_request_id
)


def create_http_exception_handler():
    """创建HTTP异常处理器"""
    async def http_exception_handler(request, exc):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": _generate_request_id()
                }
            }
        )
    return http_exception_handler


def create_general_exception_handler():
    """创建通用异常处理器"""
    async def general_exception_handler(request, exc):
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "服务器内部错误",
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": _generate_request_id()
                }
            }
        )
    return general_exception_handler

# Load environment variables
load_dotenv()

# Create FastAPI application instance
app = FastAPI(
    title="K8s SOP Advisor API",
    description="AI-powered Kubernetes Standard Operating Procedures advisor with multi-agent collaboration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://localhost:8080",  # Vue development server
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
        # Add production origins as needed
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(sop_router)

# Add exception handlers
app.add_exception_handler(HTTPException, create_http_exception_handler())
app.add_exception_handler(Exception, create_general_exception_handler())

# Basic health check endpoint
@app.get("/health")
async def health_check():
    """
    Health check endpoint to verify API is running
    """
    return JSONResponse(
        status_code=200,
        content={
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "K8s SOP Advisor API",
            "version": "1.0.0"
        }
    )

# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint providing basic API information
    """
    return {
        "message": "K8s SOP Advisor API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# Application startup event
@app.on_event("startup")
async def startup_event():
    """
    Application startup event handler
    """
    print("🚀 K8s SOP Advisor API is starting up...")
    print(f"📚 API Documentation available at: /docs")
    print(f"🔍 Health check available at: /health")

# Application shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """
    Application shutdown event handler
    """
    print("🛑 K8s SOP Advisor API is shutting down...")

if __name__ == "__main__":
    import uvicorn
    
    # Get configuration from environment variables
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    reload = os.getenv("API_RELOAD", "true").lower() == "true"
    
    print(f"🌐 Starting server on {host}:{port}")
    
    uvicorn.run(
        "web_main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )