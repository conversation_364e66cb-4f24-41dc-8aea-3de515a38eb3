import os
from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task

# Import your custom tool
from src.tools.kb_tool import KnowledgeBaseTool
from src.custom_embedder import get_embedder_config, setup_safe_environment

@CrewBase
class K8sSopCrew:
    """<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> orchestrates the agents to collaboratively enhance a Kubernetes SOP."""
    # Use absolute paths to ensure config files are found
    agents_config = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'agents.yaml')
    tasks_config = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'tasks.yaml')

    def __init__(self):
        # Set up safe environment for embeddings
        setup_safe_environment()

        # Initialize any tools that need to be shared or have state
        self.kb_tool = KnowledgeBaseTool()
        
        # Load and validate LLM configuration from environment variables
        api_key = os.getenv("OPENAI_API_KEY")
        api_base = os.getenv("OPENAI_API_BASE")
        model_name = os.getenv("OPENAI_MODEL_NAME")

        if not all([api_key, api_base, model_name]):
            raise ValueError(
                "Missing one or more required LLM environment variables: "
                "OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL_NAME. "
                "Please set them in your .env file."
            )

        if not api_base.startswith(("http://", "https://")):
            raise ValueError(
                f"Invalid OPENAI_API_BASE: '{api_base}'. "
                "It must be a valid URL (e.g., 'https://api.example.com/v1')."
            )

        # Initialize the LLM using CrewAI's built-in LLM class
        # CrewAI automatically handles OpenAI-compatible endpoints
        self.llm = LLM(
            model=f"openai/{model_name}",  # Use openai/ prefix for compatibility
            base_url=api_base,
            api_key=api_key,
            temperature=0.1
        )

    @agent
    def query_rewriter_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['query_rewriter_agent'],
            llm=self.llm,
            memory=True,  # Enable memory for this agent
            verbose=True
        )

    @agent
    def knowledge_retriever_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['knowledge_retriever_agent'],
            tools=[self.kb_tool],
            llm=self.llm,
            memory=True,  # Enable memory for this agent
            verbose=True
        )

    @agent
    def response_synthesizer_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['response_synthesizer_agent'],
            llm=self.llm,
            memory=True,  # Enable memory for this agent
            verbose=True
        )

    @task
    def rewrite_query_task(self) -> Task:
        return Task(
            config=self.tasks_config['rewrite_query_task'],
            agent=self.query_rewriter_agent()
        )

    @task
    def retrieve_knowledge_task(self) -> Task:
        return Task(
            config=self.tasks_config['retrieve_knowledge_task'],
            agent=self.knowledge_retriever_agent(),
            context=[self.rewrite_query_task()]
        )

    @task
    def synthesize_response_task(self) -> Task:
        return Task(
            config=self.tasks_config['synthesize_response_task'],
            agent=self.response_synthesizer_agent(),
            context=[self.rewrite_query_task(), self.retrieve_knowledge_task()]
        )

    @crew
    def crew(self) -> Crew:
        """Creates the K8s SOP enhancement crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            memory=True,
            # Enable context window management to prevent token limit issues
            respect_context_window=True,
            embedder=get_embedder_config(),
            # Configure memory storage path for better organization
            memory_config={
                "provider": "default",  # Use CrewAI's default memory system
                "storage_path": "./memory_storage",  # Custom storage path
            },
        )