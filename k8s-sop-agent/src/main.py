import os
from dotenv import load_dotenv
from datetime import datetime
import re

# 使用crew工厂
from src.utils.crew_factory import crew_factory
from src.utils.streaming_output import streaming_output, stream_execute

# 导入prompt_toolkit组件
from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.shortcuts import confirm
from prompt_toolkit.styles import Style
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.application import get_app

# Load environment variables from .env file
load_dotenv()

class RequirementContext:
    """单个需求的上下文管理"""

    def __init__(self, requirement_id: str, initial_question: str):
        self.requirement_id = requirement_id
        self.initial_question = initial_question
        self.question_rounds = 0
        self.max_question_rounds = 2
        self.conversation_count = 0
        self.status = "active"  # active, completed, abandoned
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.has_sop_output = False
        self.conversation_history = []

    def can_ask_more_questions(self):
        """检查是否还能继续提问"""
        return self.question_rounds < self.max_question_rounds

    def increment_question_rounds(self):
        """增加提问轮次"""
        self.question_rounds += 1
        self.last_activity = datetime.now()

    def increment_conversation(self):
        """增加对话计数"""
        self.conversation_count += 1
        self.last_activity = datetime.now()

    def mark_completed(self):
        """标记需求已完成"""
        self.status = "completed"
        self.has_sop_output = True
        self.last_activity = datetime.now()

    def reset_question_rounds(self):
        """重置提问轮次（用于新的澄清轮次）"""
        self.question_rounds = 0

    def get_context_info(self):
        """获取上下文信息"""
        return {
            'requirement_id': self.requirement_id,
            'initial_question': self.initial_question,
            'conversation_count': self.conversation_count,
            'question_rounds': self.question_rounds,
            'status': self.status,
            'has_sop_output': self.has_sop_output,
            'conversation_history': self.conversation_history
        }

class IntelligentSessionTracker:
    """
    智能会话跟踪器 - 支持多需求管理和智能状态切换
    """

    def __init__(self):
        self.session_id = f"sop_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.total_conversation_count = 0
        self.requirements = {}  # requirement_id -> RequirementContext
        self.current_requirement_id = None
        self.requirement_counter = 0

    def detect_new_requirement(self, question: str, global_history: list) -> bool:
        """
        基于CrewAI的智能需求检测
        """
        # 如果没有当前需求，肯定是新需求
        if not self.current_requirement_id:
            return True

        current_req = self.requirements[self.current_requirement_id]

        # 使用CrewAI进行意图识别
        return self._crewai_detect_new_requirement(
            question=question,
            current_requirement=current_req,
            global_history=global_history
        )

    def _crewai_detect_new_requirement(self, question: str, current_requirement: 'RequirementContext', global_history: list) -> bool:
        """
        使用CrewAI意图识别系统检测是否为新需求
        """
        try:
            from src.crews.intent_crew import IntentCrew

            # 创建意图识别Crew
            intent_crew = IntentCrew(session_id=self.session_id)

            # 准备当前需求信息
            current_requirement_info = {
                'requirement_id': current_requirement.requirement_id,
                'initial_question': current_requirement.initial_question,
                'status': current_requirement.status,
                'has_sop_output': current_requirement.has_sop_output,
                'conversation_count': current_requirement.conversation_count,
                'recent_history': current_requirement.conversation_history[-3:] if current_requirement.conversation_history else []
            }

            # 执行意图分析
            result = intent_crew.analyze_intent(
                question=question,
                current_requirement_info=current_requirement_info,
                conversation_history=current_requirement.conversation_history
            )

            is_new_requirement = result.get('is_new_requirement', False)
            print(f"🤖 CrewAI意图识别: {'新需求' if is_new_requirement else '继续当前需求'}")

            # 存储完整的意图分析结果，供后续使用
            self._last_intent_analysis = result

            return is_new_requirement

        except Exception as e:
            print(f"⚠️ CrewAI意图识别失败，使用备用逻辑: {e}")
            # 备用逻辑：简单的主题差异检测
            return self._fallback_detect_new_requirement(question, current_requirement)

    def _fallback_detect_new_requirement(self, question: str, current_requirement: 'RequirementContext') -> bool:
        """
        备用的新需求检测逻辑（当CrewAI不可用时）
        """
        # 简化的主题差异检测
        return self._is_different_topic(question, current_requirement.initial_question)

    def get_last_intent_analysis(self) -> dict:
        """获取最后一次的意图分析结果"""
        return getattr(self, '_last_intent_analysis', {})

    def _is_different_topic(self, new_question: str, initial_question: str) -> bool:
        """检测是否为不同主题"""
        # 提取K8s资源类型
        k8s_resources = [
            'pod', 'service', 'deployment', 'configmap', 'secret',
            'ingress', 'namespace', 'pv', 'pvc', 'statefulset',
            'daemonset', 'job', 'cronjob'
        ]

        def extract_resources(text):
            text_lower = text.lower()
            found_resources = []
            for resource in k8s_resources:
                if resource in text_lower:
                    found_resources.append(resource)
            return set(found_resources)

        new_resources = extract_resources(new_question)
        initial_resources = extract_resources(initial_question)

        # 如果资源类型完全不同，认为是不同主题
        if new_resources and initial_resources and not new_resources.intersection(initial_resources):
            return True

        return False

    def start_new_requirement(self, question: str) -> str:
        """开始新需求"""
        self.requirement_counter += 1
        requirement_id = f"req_{self.requirement_counter:03d}"

        self.requirements[requirement_id] = RequirementContext(requirement_id, question)
        self.current_requirement_id = requirement_id

        return requirement_id

    def get_current_requirement(self) -> RequirementContext:
        """获取当前需求上下文"""
        if not self.current_requirement_id:
            return None
        return self.requirements.get(self.current_requirement_id)

    def increment_conversation(self):
        """增加对话计数"""
        self.total_conversation_count += 1
        if self.current_requirement_id:
            self.requirements[self.current_requirement_id].increment_conversation()

    def get_session_info(self):
        """获取会话信息"""
        current_req = self.get_current_requirement()
        if current_req:
            req_info = f"需求{current_req.requirement_id} | 轮次: {current_req.conversation_count} | 提问: {current_req.question_rounds}/{current_req.max_question_rounds}"
        else:
            req_info = "无活跃需求"

        return f"会话: {self.session_id} | 总对话: {self.total_conversation_count} | {req_info}"

    def get_requirement_history(self, requirement_id: str = None) -> list:
        """获取指定需求的对话历史"""
        if not requirement_id:
            requirement_id = self.current_requirement_id

        if requirement_id and requirement_id in self.requirements:
            return self.requirements[requirement_id].conversation_history
        return []

def is_asking_for_more_info(output: str) -> bool:
    """
    检测输出是否为询问更多信息的问题
    """
    # 检测强烈询问意图的关键词
    strong_asking_patterns = [
        r"请一次性回答以下问题",
        r"需要确认.*关键信息",
        r"缺少.*关键信息.*无法",
        r"必须.*提供.*才能",
        r"请告诉我.*具体的",
        r"无法.*推断.*需要",
    ]
    
    output_lower = output.lower()
    
    # 检查是否包含强烈询问模式
    for pattern in strong_asking_patterns:
        if re.search(pattern, output_lower):
            return True
    
    # 检查是否有连续的编号问题（3个或以上）
    numbered_questions = re.findall(r'\d+\.\s*[^。]*\?', output)
    if len(numbered_questions) >= 3:
        return True
    
    # 检查是否包含明确的信息缺失声明
    missing_info_patterns = [
        r"缺少.*信息",
        r"信息不足",
        r"需要.*更多.*信息",
    ]
    
    missing_info_count = 0
    for pattern in missing_info_patterns:
        if re.search(pattern, output_lower):
            missing_info_count += 1
    
    # 只有当同时满足多个条件时才认为是询问
    question_marks = output.count('?') + output.count('？')
    
    return missing_info_count >= 2 and question_marks >= 2

def _should_skip_requirements_analysis(question: str, conversation_history: list) -> bool:
    """
    智能判断是否应该跳过需求分析阶段 (旧版本，保持兼容性)
    """
    return _should_skip_requirements_analysis_v2(question, conversation_history, False, False)

def _should_skip_requirements_analysis_v2(question: str, requirement_history: list, has_sop_output: bool, is_new_requirement: bool, session_tracker=None) -> bool:
    """
    基于CrewAI的智能跳过需求分析判断 - 增强版
    """
    try:
        # 如果有session_tracker且有最近的意图分析结果，直接使用
        if session_tracker and hasattr(session_tracker, '_last_intent_analysis'):
            intent_result = session_tracker._last_intent_analysis
            if intent_result and 'skip_requirements_analysis' in intent_result:
                should_skip = intent_result['skip_requirements_analysis']
                print(f"🤖 CrewAI跳过分析判断: {'跳过' if should_skip else '不跳过'}")
                return should_skip

        # 如果没有意图分析结果，使用备用逻辑
        return _fallback_should_skip_requirements_analysis(
            question, requirement_history, has_sop_output, is_new_requirement
        )
    except Exception as e:
        print(f"⚠️ 跳过分析判断失败，使用备用逻辑: {e}")
        return _fallback_should_skip_requirements_analysis(
            question, requirement_history, has_sop_output, is_new_requirement
        )



def _fallback_should_skip_requirements_analysis(question: str, requirement_history: list, has_sop_output: bool, is_new_requirement: bool) -> bool:
    """
    备用的跳过需求分析判断逻辑
    """
    question_lower = question.lower()

    # 简化的直接生成关键词检测
    direct_patterns = ["直接生成", "立即生成", "不用问", "别问了", "跳过"]
    for pattern in direct_patterns:
        if pattern in question_lower:
            return True

    # 如果已有输出且是调整请求
    if has_sop_output:
        adjustment_patterns = ["调整", "修改", "改进", "优化", "完善"]
        for pattern in adjustment_patterns:
            if pattern in question_lower:
                return True

    return False

def _should_use_fast_mode(question: str, conversation_history: list) -> bool:
    """
    智能判断是否应该使用快速模式 (旧版本，保持兼容性)
    """
    return _should_use_fast_mode_v2(question, conversation_history, False)

def _should_use_fast_mode_v2(question: str, requirement_history: list, has_sop_output: bool, session_tracker=None) -> bool:
    """
    基于CrewAI的智能快速模式判断 - 增强版
    """
    try:
        # 如果有session_tracker且有最近的意图分析结果，直接使用
        if session_tracker and hasattr(session_tracker, '_last_intent_analysis'):
            intent_result = session_tracker._last_intent_analysis
            if intent_result and 'use_fast_mode' in intent_result:
                use_fast = intent_result['use_fast_mode']
                print(f"🤖 CrewAI模式选择: {'快速模式' if use_fast else '完整模式'}")
                return use_fast

        # 如果没有意图分析结果，使用备用逻辑
        return _fallback_should_use_fast_mode(question, requirement_history, has_sop_output)
    except Exception as e:
        print(f"⚠️ 快速模式判断失败，使用备用逻辑: {e}")
        return _fallback_should_use_fast_mode(question, requirement_history, has_sop_output)



def _fallback_should_use_fast_mode(question: str, requirement_history: list, has_sop_output: bool) -> bool:
    """
    备用的快速模式判断逻辑
    """
    question_lower = question.lower()

    # 简化的快速模式关键词
    fast_keywords = ["快速", "快点", "急", "紧急", "简单", "基础"]
    for keyword in fast_keywords:
        if keyword in question_lower:
            return True

    # 简单调整请求
    if has_sop_output:
        adjustment_keywords = ["调整一下", "修改一点", "简单调整", "小修改"]
        for keyword in adjustment_keywords:
            if keyword in question_lower:
                return True

    return False

def generate_sop_with_flow_control(question: str, session_tracker: IntelligentSessionTracker, global_conversation_history: list):
    """
    使用智能流程控制生成SOP文档 - 支持智能需求管理和流式输出
    """
    import time
    start_time = time.time()

    # 1. 智能检测是否为新需求
    is_new_requirement = session_tracker.detect_new_requirement(question, global_conversation_history)

    if is_new_requirement:
        print("🆕 检测到新需求，开始新的对话流程")
        requirement_id = session_tracker.start_new_requirement(question)
        print(f"📋 需求ID: {requirement_id}")
    else:
        print("🔄 继续当前需求的对话")

    # 2. 获取当前需求上下文
    current_req = session_tracker.get_current_requirement()
    if not current_req:
        # 如果没有当前需求，创建一个
        requirement_id = session_tracker.start_new_requirement(question)
        current_req = session_tracker.get_current_requirement()
        print(f"📋 创建新需求: {requirement_id}")

    # 3. 更新对话计数
    session_tracker.increment_conversation()
    current_req.increment_conversation()

    # 4. 获取当前需求的对话历史
    requirement_history = current_req.conversation_history

    # 5. 智能跳过机制 - 基于当前需求的历史和CrewAI意图分析
    should_skip_requirements = _should_skip_requirements_analysis_v2(
        question, requirement_history, current_req.has_sop_output, is_new_requirement, session_tracker
    )

    # 6. 准备输入
    full_context = "\n".join(requirement_history)
    inputs = {
        'question': question,
        'chat_history': full_context,
        'question_context': f'需求{current_req.requirement_id} - 轮次: {current_req.conversation_count}'
    }

    # 7. 需求分析阶段
    if should_skip_requirements:
        print("⚡ 检测到直接生成请求，跳过需求分析阶段")
    else:
        # 第一阶段：需求收集
        print("🔍 分析需求中...")
        req_start_time = time.time()
        requirements_crew = crew_factory.get_requirements_crew(session_tracker.session_id)
        
        # 使用流式输出执行需求分析
        requirements_result = stream_execute(
            requirements_crew.crew().kickoff,
            inputs,
            "💬 需求分析"
        )
        
        req_end_time = time.time()
        print(f"⏱️ 需求分析耗时: {req_end_time - req_start_time:.1f}秒")

        requirements_output = str(requirements_result.raw)

        # 检查是否需要更多信息
        if is_asking_for_more_info(requirements_output):
            print(f"📝 需要更多信息 (当前轮次: {current_req.question_rounds}/{current_req.max_question_rounds})")

            # 如果还可以提问，返回问题给用户
            if current_req.can_ask_more_questions():
                # 只有在真正返回问题给用户时才增加计数
                current_req.increment_question_rounds()
                print(f"📝 提问轮次更新为: {current_req.question_rounds}/{current_req.max_question_rounds}")

                # 更新需求的对话历史
                current_req.conversation_history.append(f"User: {question}")
                current_req.conversation_history.append(f"AI: {requirements_output}")

                return requirements_result.raw
            else:
                # 已达到最大提问轮次，强制生成SOP
                print(f"⚠️ 已达到最大提问轮次，基于现有信息生成SOP...")
        else:
            # 信息足够，不需要提问
            print(f"✅ 信息充足，直接生成SOP文档")

    # 8. SOP生成阶段
    # 智能选择SOP生成模式 - 基于当前需求的历史和CrewAI意图分析
    use_fast_mode = _should_use_fast_mode_v2(question, requirement_history, current_req.has_sop_output, session_tracker)

    if use_fast_mode:
        print("⚡ 使用快速模式生成SOP文档...")
        sop_start_time = time.time()
        fast_crew = crew_factory.get_fast_sop_crew(session_tracker.session_id)
        
        # 使用流式输出执行快速SOP生成
        final_result = stream_execute(
            fast_crew.crew().kickoff,
            inputs,
            "📄 快速SOP生成"
        )
        
        sop_end_time = time.time()
        print(f"⏱️ 快速SOP生成耗时: {sop_end_time - sop_start_time:.1f}秒")
    else:
        print("📄 使用完整模式生成SOP文档...")
        sop_start_time = time.time()
        base_crew = crew_factory.get_base_sop_crew(session_tracker.session_id)
        
        # 使用流式输出执行完整SOP生成
        final_result = stream_execute(
            base_crew.crew().kickoff,
            inputs,
            "📄 完整SOP生成"
        )
        
        sop_end_time = time.time()
        print(f"⏱️ 完整SOP生成耗时: {sop_end_time - sop_start_time:.1f}秒")

    # 9. 更新需求状态
    current_req.mark_completed()  # 标记需求已完成
    current_req.reset_question_rounds()  # 重置提问轮次，为后续调整做准备

    # 10. 更新对话历史
    current_req.conversation_history.append(f"User: {question}")
    current_req.conversation_history.append(f"AI: {final_result.raw}")

    total_time = time.time() - start_time
    print(f"⏱️ 总耗时: {total_time:.1f}秒")
    print(f"📊 需求状态: {current_req.status} | 对话轮次: {current_req.conversation_count}")

    return final_result.raw

def create_enhanced_input_system():
    """
    创建增强的输入系统，优化中文输入体验
    """
    # 创建命令历史记录
    history = InMemoryHistory()

    # 创建自动补全词汇
    k8s_keywords = [
        # 基础概念
        "Pod", "Deployment", "Service", "ConfigMap", "Secret", "Namespace",
        "Ingress", "PersistentVolume", "PersistentVolumeClaim", "StorageClass",
        "StatefulSet", "DaemonSet", "Job", "CronJob", "ReplicaSet",

        # 操作动词
        "部署", "创建", "删除", "更新", "扩容", "缩容", "重启", "回滚",
        "升级", "降级", "迁移", "备份", "恢复", "监控", "调试",

        # 常用短语
        "生成SOP", "创建文档", "部署应用", "配置服务", "设置监控",
        "故障排查", "性能优化", "安全配置", "网络配置", "存储配置",

        # 环境类型
        "生产环境", "测试环境", "开发环境", "预发布环境",

        # 快速命令
        "快速生成", "直接生成", "简单配置", "详细文档", "完整流程"
    ]

    completer = WordCompleter(k8s_keywords, ignore_case=True)

    # 创建样式
    style = Style.from_dict({
        'prompt': '#00aa00 bold',           # 绿色粗体提示符
        'input': '#ffffff',                 # 白色输入文本
        'completion-menu.completion': '#444444 bg:#ffffff',  # 补全菜单样式
        'completion-menu.completion.current': '#ffffff bg:#444444',  # 当前选中项
        'bottom-toolbar': '#444444 bg:#bbbbbb',  # 底部工具栏
    })

    # 创建键绑定
    bindings = KeyBindings()

    @bindings.add('c-c')  # Ctrl+C
    def _(event):
        """处理Ctrl+C中断"""
        event.app.exit(exception=KeyboardInterrupt)

    @bindings.add('c-d')  # Ctrl+D
    def _(event):
        """处理Ctrl+D退出"""
        event.app.exit()

    return {
        'history': history,
        'completer': completer,
        'style': style,
        'key_bindings': bindings
    }

def get_user_input(prompt_text: str, input_system: dict) -> str:
    """
    获取用户输入，支持中文输入优化
    """
    try:
        # 创建底部工具栏信息
        def get_bottom_toolbar():
            return HTML(
                '💡 <b>提示:</b> '
                'Tab键自动补全 | '
                '↑↓键浏览历史 | '
                'Ctrl+C退出 | '
                '支持中文输入'
            )

        # 使用prompt_toolkit获取输入
        user_input = prompt(
            HTML(f'<prompt>{prompt_text}</prompt>'),
            history=input_system['history'],
            completer=input_system['completer'],
            style=input_system['style'],
            key_bindings=input_system['key_bindings'],
            bottom_toolbar=get_bottom_toolbar,
            complete_style='multi-column',  # 多列显示补全
            mouse_support=True,             # 支持鼠标
            wrap_lines=True,                # 自动换行
            multiline=False,                # 单行输入
            vi_mode=False,                  # 使用Emacs模式（对中文更友好）
            enable_history_search=True,     # 启用历史搜索
            search_ignore_case=True,        # 搜索忽略大小写
        )

        return user_input.strip()

    except KeyboardInterrupt:
        raise KeyboardInterrupt
    except EOFError:
        # Ctrl+D 被按下
        return "exit"
    except Exception as e:
        print(f"⚠️ 输入系统错误: {e}")
        # 回退到标准输入
        return input(prompt_text).strip()

def show_help():
    """显示帮助信息"""
    help_text = """
📚 K8s SOP文档生成器 - 帮助信息
═══════════════════════════════════════════════════════════════

🎯 主要功能:
  • 智能生成Kubernetes标准操作程序(SOP)文档
  • 支持多轮对话，自动收集必要信息
  • 智能判断是否跳过需求分析阶段
  • 支持快速模式和完整模式

💡 使用技巧:
  • 描述要操作的K8s资源类型（Pod、Service、Deployment等）
  • 说明操作目的（部署、升级、故障排查等）
  • 提及环境信息（生产、测试、开发环境）
  • 包含特殊要求（时间窗口、安全要求等）

🔧 特殊命令:
  • help/帮助/?     - 显示此帮助信息
  • clear/清屏      - 清屏
  • status/状态     - 显示当前会话状态
  • exit/quit/退出  - 退出程序

⌨️ 快捷键:
  • Tab键          - 自动补全K8s相关词汇
  • ↑↓键          - 浏览输入历史
  • Ctrl+C         - 退出程序
  • Ctrl+D         - 退出程序

📝 示例输入:
  • "生成Pod部署SOP"
  • "创建Service配置文档"
  • "Deployment滚动更新流程"
  • "故障排查指南"
  • "快速生成简单的ConfigMap配置"

═══════════════════════════════════════════════════════════════
"""
    print(help_text)

def validate_input(user_input: str) -> tuple[bool, str]:
    """
    验证用户输入
    返回: (是否有效, 错误信息)
    """
    if not user_input or not user_input.strip():
        return False, "输入不能为空"

    if len(user_input.strip()) < 3:
        return False, "请输入更详细的需求描述（至少3个字符）"

    # 检查是否包含有意义的内容
    meaningful_chars = sum(1 for c in user_input if c.isalnum() or c in "，。！？、")
    if meaningful_chars < 2:
        return False, "请输入有意义的需求描述"

    return True, ""

def format_output(result: str, session_tracker: IntelligentSessionTracker) -> str:
    """格式化输出结果"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    header = f"""
{'='*80}
📋 SOP文档生成结果 | {timestamp}
📁 {session_tracker.get_session_info()}
{'='*80}
"""

    footer = f"""
{'='*80}
💡 提示: 您可以继续提问来完善或调整此SOP文档
{'='*80}
"""

    return header + result + footer

def main():
    """
    K8s SOP文档生成器 - 增强输入体验版本
    """
    print("📄 K8s SOP文档生成器 (增强版)")
    print("=" * 60)
    print("💬 与AI对话，智能生成Kubernetes SOP文档")
    print("🤖 输入您的需求，系统将自动判断是否需要更多信息")
    print("🚀 支持中文输入、自动补全、历史记录等增强功能")
    print("=" * 60)

    try:
        # 初始化智能会话跟踪器
        session_tracker = IntelligentSessionTracker()
        print(f"📁 {session_tracker.get_session_info()}")

        # 初始化全局对话历史（用于显示和兼容性）
        global_conversation_history = []

        # 创建增强输入系统
        print("\n🔧 初始化增强输入系统...")
        input_system = create_enhanced_input_system()
        print("✅ 输入系统就绪")

        print("\n💡 使用说明:")
        print("  • Tab键: 自动补全K8s相关词汇")
        print("  • ↑↓键: 浏览输入历史")
        print("  • Ctrl+C: 退出程序")
        print("  • 输入 'exit', 'quit', '退出', '结束': 正常退出")
        print("  • 支持中文输入和多行编辑")
        print("-" * 60)
        
        while True:
            try:
                # 使用增强输入系统
                prompt_text = f"[{session_tracker.total_conversation_count + 1}] 您的需求: "
                user_input = get_user_input(prompt_text, input_system)

                # 处理退出命令
                if user_input.lower() in ["exit", "quit", "退出", "结束", ""]:
                    if user_input == "" or confirm("确认退出程序吗？"):
                        print("👋 感谢使用K8s SOP文档生成器，再见！")
                        break
                    else:
                        continue

                # 跳过空输入
                if not user_input.strip():
                    continue

                # 处理特殊命令
                if user_input.lower() in ["help", "帮助", "?"]:
                    show_help()
                    continue
                elif user_input.lower() in ["clear", "清屏"]:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    continue
                elif user_input.lower() in ["status", "状态"]:
                    print(f"📊 {session_tracker.get_session_info()}")
                    print(f"📝 全局对话历史条数: {len(global_conversation_history)}")
                    current_req = session_tracker.get_current_requirement()
                    if current_req:
                        print(f"📋 当前需求历史条数: {len(current_req.conversation_history)}")
                        print(f"🎯 当前需求状态: {current_req.status}")
                    continue

                # 输入验证
                is_valid, error_msg = validate_input(user_input)
                if not is_valid:
                    print(f"⚠️ {error_msg}")
                    continue

                # 生成SOP文档
                print("\n🚀 开始生成SOP文档...")
                try:
                    result = generate_sop_with_flow_control(user_input, session_tracker, global_conversation_history)

                    # 更新全局对话历史（用于显示）
                    global_conversation_history.append(f"User: {user_input}")
                    global_conversation_history.append(f"AI: {result}")

                    # 格式化并显示结果
                    formatted_result = format_output(result, session_tracker)
                    print(formatted_result)
                    
                except Exception as e:
                    print(f"❌ 生成SOP文档时发生错误: {e}")
                    print("请重新描述您的需求或检查网络连接")
            
            except KeyboardInterrupt:
                print("\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                print("请重试或输入 'exit' 退出")
    
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请检查配置和环境变量")

if __name__ == "__main__":
    main()