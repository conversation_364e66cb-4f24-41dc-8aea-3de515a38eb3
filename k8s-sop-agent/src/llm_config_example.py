"""
示例：使用CrewAI内置LLM配置替代自定义LLM

这个文件展示了如何使用CrewAI的内置LLM类来替代自定义的CustomLLM实现。
CrewAI通过LiteLLM支持各种OpenAI兼容的API端点。
"""

import os
from crewai import LLM, Agent, Task, Crew

# 方法1: 使用环境变量配置 (推荐)
def create_llm_from_env():
    """
    使用环境变量配置LLM
    需要在.env文件中设置：
    MODEL=openai/your-model-name
    OPENAI_API_KEY=your-api-key
    OPENAI_API_BASE=https://your-endpoint.com/v1
    """
    return LLM(
        model=os.getenv("MODEL", "openai/gpt-4"),
        temperature=0.7
    )

# 方法2: 直接代码配置
def create_llm_direct():
    """直接在代码中配置LLM参数"""
    return LLM(
        model="openai/qwen/qwq-32b:free",  # 使用provider/model格式
        base_url="https://openrouter.ai/api/v1",
        api_key="sk-or-v1-your-api-key-here",
        temperature=0.7,
        max_tokens=4000,
        timeout=120
    )

# 方法3: 支持多种提供商的配置
def create_llm_for_different_providers():
    """展示不同LLM提供商的配置方式"""
    
    # OpenAI兼容接口 (如OpenRouter, Groq等)
    openai_compatible = LLM(
        model="openai/qwen/qwq-32b:free",
        base_url="https://openrouter.ai/api/v1",
        api_key="your-api-key"
    )
    
    # Anthropic Claude
    claude = LLM(
        model="anthropic/claude-3-sonnet-20240229",
        api_key="your-anthropic-key"
    )
    
    # Google Gemini
    gemini = LLM(
        model="gemini/gemini-pro",
        api_key="your-gemini-key"
    )
    
    # 本地Ollama
    ollama = LLM(
        model="ollama/llama2",
        base_url="http://localhost:11434"
    )
    
    return {
        "openai_compatible": openai_compatible,
        "claude": claude,
        "gemini": gemini,
        "ollama": ollama
    }

# 方法4: 高级配置选项
def create_advanced_llm():
    """展示高级配置选项"""
    return LLM(
        model="openai/gpt-4",
        base_url="https://api.openai.com/v1",
        api_key="your-api-key",
        temperature=0.7,
        max_tokens=4000,
        top_p=0.9,
        frequency_penalty=0.1,
        presence_penalty=0.1,
        timeout=120,
        stream=True,  # 启用流式响应
        # response_format={"type": "json"},  # 结构化输出
        seed=42  # 确保可重现的结果
    )

# 使用示例
def example_usage():
    """展示如何在CrewAI中使用配置的LLM"""
    
    # 创建LLM实例
    llm = create_llm_from_env()
    
    # 创建Agent
    agent = Agent(
        role="Research Specialist",
        goal="Conduct comprehensive research and analysis",
        backstory="A dedicated research professional with years of experience",
        llm=llm,
        verbose=True
    )
    
    # 创建Task
    task = Task(
        description="Research the latest trends in Kubernetes operations",
        expected_output="A comprehensive report on Kubernetes operations trends",
        agent=agent
    )
    
    # 创建Crew
    crew = Crew(
        agents=[agent],
        tasks=[task],
        verbose=True
    )
    
    return crew

if __name__ == "__main__":
    # 测试不同的配置方法
    print("Testing LLM configurations...")
    
    # 测试环境变量配置
    try:
        llm_env = create_llm_from_env()
        print("✓ Environment variable configuration works")
    except Exception as e:
        print(f"✗ Environment variable configuration failed: {e}")
    
    # 测试直接配置
    try:
        llm_direct = create_llm_direct()
        print("✓ Direct configuration works")
    except Exception as e:
        print(f"✗ Direct configuration failed: {e}")
    
    print("Configuration examples completed!")
