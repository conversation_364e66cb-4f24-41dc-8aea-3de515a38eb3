"""
Flow状态模型定义
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from enum import Enum


class QuestionStatus(str, Enum):
    """问题状态枚举"""
    INITIAL = "initial"
    NEEDS_MORE_INFO = "needs_more_info"
    COMPLETED = "completed"
    ERROR = "error"


class SopState(BaseModel):
    """SOP建议流程状态模型"""
    
    # 会话信息
    session_id: str = Field(default="", description="会话ID")
    request_id: str = Field(default="", description="请求ID")
    
    # 用户输入
    question: str = Field(default="", description="用户问题")
    context: Optional[Dict[str, Any]] = Field(default=None, description="额外上下文")
    
    # 处理状态
    question_status: QuestionStatus = Field(default=QuestionStatus.INITIAL, description="问题状态")
    question_round: int = Field(default=0, description="问题轮次")
    max_rounds: int = Field(default=2, description="最大问题轮次")
    
    # 中间结果
    requirements_output: Optional[str] = Field(default=None, description="需求分析输出")
    final_output: Optional[str] = Field(default=None, description="最终SOP输出")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def can_ask_more_questions(self) -> bool:
        """检查是否可以继续提问"""
        return self.question_round < self.max_rounds
    
    def increment_question_round(self):
        """增加问题轮次"""
        self.question_round += 1
    
    def reset_question_rounds(self):
        """重置问题轮次"""
        self.question_round = 0
        self.question_status = QuestionStatus.INITIAL