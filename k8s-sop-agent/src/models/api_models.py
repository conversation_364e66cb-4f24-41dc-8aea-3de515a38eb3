"""
API数据模型定义
定义所有API请求和响应的Pydantic模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class SessionStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    EXPIRED = "expired"
    DELETED = "deleted"


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"


# === SOP建议相关模型 ===

class SOPAdviceRequest(BaseModel):
    """SOP建议请求模型"""
    question: str = Field(..., min_length=1, max_length=2000, description="用户问题")
    session_id: Optional[str] = Field(None, description="会话ID，如果不提供将创建新会话")
    context: Optional[Dict[str, Any]] = Field(None, description="额外的上下文信息")
    
    @validator('question')
    def validate_question(cls, v):
        if not v.strip():
            raise ValueError('问题不能为空')
        return v.strip()


class SOPAdviceResponse(BaseModel):
    """SOP建议响应模型"""
    session_id: str = Field(..., description="会话ID")
    response: str = Field(..., description="系统回复")
    is_question: bool = Field(..., description="是否为后续询问")
    question_round: int = Field(..., description="当前问题轮次")
    max_rounds: int = Field(..., description="最大问题轮次")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据信息")


# === 会话管理相关模型 ===

class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str = Field(..., description="会话ID")
    created_at: datetime = Field(..., description="创建时间")
    last_activity: datetime = Field(..., description="最后活动时间")
    message_count: int = Field(..., ge=0, description="消息数量")
    status: SessionStatus = Field(..., description="会话状态")


class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    session_id: Optional[str] = Field(None, description="指定会话ID，如果不提供将自动生成")


class CreateSessionResponse(BaseModel):
    """创建会话响应模型"""
    session_id: str = Field(..., description="会话ID")
    created_at: datetime = Field(..., description="创建时间")


class SessionListResponse(BaseModel):
    """会话列表响应模型"""
    sessions: List[SessionInfo] = Field(..., description="会话列表")
    total: int = Field(..., ge=0, description="总数量")


# === 文档管理相关模型 ===

class DocumentInfo(BaseModel):
    """文档信息模型"""
    doc_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    collection: str = Field(..., description="所属集合")
    upload_time: datetime = Field(..., description="上传时间")
    file_size: int = Field(..., ge=0, description="文件大小（字节）")
    content_hash: str = Field(..., description="内容哈希")
    status: DocumentStatus = Field(..., description="文档状态")


class UploadDocumentResponse(BaseModel):
    """上传文档响应模型"""
    doc_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    status: DocumentStatus = Field(..., description="处理状态")
    message: str = Field(..., description="处理消息")


class DocumentListResponse(BaseModel):
    """文档列表响应模型"""
    documents: List[DocumentInfo] = Field(..., description="文档列表")
    total: int = Field(..., ge=0, description="总数量")


# === 系统监控相关模型 ===

class HealthStatus(str, Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class ComponentHealth(BaseModel):
    """组件健康状态模型"""
    name: str = Field(..., description="组件名称")
    status: HealthStatus = Field(..., description="健康状态")
    message: Optional[str] = Field(None, description="状态消息")
    last_check: datetime = Field(..., description="最后检查时间")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: HealthStatus = Field(..., description="整体健康状态")
    timestamp: datetime = Field(..., description="检查时间")
    components: List[ComponentHealth] = Field(..., description="组件状态列表")
    uptime_seconds: int = Field(..., ge=0, description="运行时间（秒）")


class SystemMetrics(BaseModel):
    """系统指标模型"""
    timestamp: datetime = Field(..., description="指标时间")
    active_sessions: int = Field(..., ge=0, description="活跃会话数")
    total_requests: int = Field(..., ge=0, description="总请求数")
    error_rate: float = Field(..., ge=0, le=1, description="错误率")
    avg_response_time_ms: float = Field(..., ge=0, description="平均响应时间（毫秒）")
    memory_usage_mb: float = Field(..., ge=0, description="内存使用量（MB）")
    cpu_usage_percent: float = Field(..., ge=0, le=100, description="CPU使用率（%）")


# === 错误响应模型 ===

class ErrorDetail(BaseModel):
    """错误详情模型"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    field: Optional[str] = Field(None, description="相关字段")


class ErrorResponse(BaseModel):
    """API错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    error: ErrorDetail = Field(..., description="错误详情")
    timestamp: datetime = Field(..., description="错误时间")
    request_id: str = Field(..., description="请求ID")


class ValidationErrorResponse(BaseModel):
    """验证错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    error: ErrorDetail = Field(..., description="错误详情")
    validation_errors: List[ErrorDetail] = Field(..., description="验证错误列表")
    timestamp: datetime = Field(..., description="错误时间")
    request_id: str = Field(..., description="请求ID")


# === 通用响应模型 ===

class APIResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    data: Optional[Any] = Field(None, description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")
    timestamp: datetime = Field(..., description="响应时间")
    request_id: str = Field(..., description="请求ID")


class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., ge=0, description="总数量")
    page: int = Field(..., ge=1, description="当前页码")
    size: int = Field(..., ge=1, description="每页大小")
    pages: int = Field(..., ge=0, description="总页数")
    
    @validator('pages', pre=True, always=True)
    def calculate_pages(cls, v, values):
        total = values.get('total', 0)
        size = values.get('size', 1)
        return (total + size - 1) // size if total > 0 else 0


# === 认证相关模型 ===

class TokenRequest(BaseModel):
    """令牌请求模型"""
    username: str = Field(..., min_length=1, description="用户名")
    password: str = Field(..., min_length=1, description="密码")


class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


# === 配置相关模型 ===

class APIConfig(BaseModel):
    """API配置模型"""
    title: str = Field("K8s SOP Advisor API", description="API标题")
    version: str = Field("1.0.0", description="API版本")
    description: str = Field("Kubernetes SOP顾问Web API", description="API描述")
    max_request_size: int = Field(10 * 1024 * 1024, description="最大请求大小（字节）")
    request_timeout: int = Field(300, description="请求超时时间（秒）")