"""
SOP建议流程 - 使用CrewAI Flows模式
"""

from crewai.flow.flow import Flow, listen, router, start
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uuid
from datetime import datetime

from src.models.flow_models import SopState, QuestionStatus
from src.crews import RequirementsCrew, BaseSopCrew
from src.utils.cache_manager import cache_manager
from src.utils.streaming_output import stream_execute


class SopAdviceFlow(Flow[SopState]):
    """SOP建议流程"""
    
    def __init__(self, session_id: Optional[str] = None):
        super().__init__()
        self.session_id = session_id or f"flow_session_{uuid.uuid4().hex[:8]}"
        
    @start()
    def initialize_flow(self):
        """初始化流程"""
        self.state.session_id = self.session_id
        self.state.request_id = str(uuid.uuid4())
        self.state.question_status = QuestionStatus.INITIAL
        self.state.metadata["start_time"] = datetime.utcnow().isoformat()
        
        return "flow_initialized"
    
    @listen(initialize_flow)
    def check_cache(self, _):
        """检查缓存"""
        cache_key = {
            "question": self.state.question,
            "context": self.state.context,
            "session_id": self.state.session_id
        }
        
        cached_response = cache_manager.get(cache_key)
        if cached_response:
            self.state.final_output = cached_response.response
            self.state.question_status = QuestionStatus.COMPLETED
            self.state.metadata["cache_hit"] = True
            return "cache_hit"
        
        self.state.metadata["cache_hit"] = False
        return "cache_miss"
    
    @router(check_cache)
    def route_from_cache(self, cache_result):
        """根据缓存结果路由"""
        if cache_result == "cache_hit":
            return "complete"
        return "process_requirements"
    
    @listen("process_requirements")
    def analyze_requirements(self):
        """需求分析"""
        try:
            requirements_crew = RequirementsCrew()
            
            # 构建上下文，包含轮次信息
            question_context = f"Flow流程 - 第{self.state.question_round + 1}轮询问"
            
            requirements_result = stream_execute(
                requirements_crew.crew().kickoff,
                {
                    'question': self.state.question,
                    'chat_history': '',
                    'question_context': question_context
                },
                "需求分析"
            )
            
            self.state.requirements_output = str(requirements_result.raw)
            
            # 检测是否需要更多信息
            from src.api.sop_endpoints_simple import _is_asking_for_more_info
            is_question = _is_asking_for_more_info(self.state.requirements_output)
            
            if is_question:
                # 检查是否达到最大轮次
                if self.state.can_ask_more_questions():
                    self.state.question_status = QuestionStatus.NEEDS_MORE_INFO
                    self.state.increment_question_round()
                    self.state.metadata["current_round"] = self.state.question_round
                    return "needs_more_info"
                else:
                    # 已达到最大轮次，强制生成SOP
                    print(f"⚠️ 已达到最大询问轮次({self.state.max_rounds})，基于现有信息生成SOP")
                    self.state.metadata["max_rounds_reached"] = True
                    return "force_generate_sop"
            else:
                return "requirements_complete"
                
        except Exception as e:
            self.state.question_status = QuestionStatus.ERROR
            self.state.metadata["error"] = str(e)
            return "error"
    
    @listen("requirements_complete")
    @listen("force_generate_sop")
    def generate_sop(self):
        """生成SOP"""
        try:
            sop_crew = BaseSopCrew()
            
            # 根据是否强制生成来调整上下文
            if "max_rounds_reached" in self.state.metadata:
                question_context = "Flow流程 - 强制生成(达到最大轮次)"
            else:
                question_context = "Flow流程 - 信息充足"
            
            final_result = stream_execute(
                sop_crew.crew().kickoff,
                {
                    'question': self.state.question,
                    'chat_history': '',
                    'question_context': question_context
                },
                "SOP生成"
            )
            
            self.state.final_output = str(final_result.raw)
            self.state.question_status = QuestionStatus.COMPLETED
            
            # 缓存结果（只在信息充足时缓存，不缓存强制生成的结果）
            if "max_rounds_reached" not in self.state.metadata:
                cache_key = {
                    "question": self.state.question,
                    "context": self.state.context,
                    "session_id": self.state.session_id
                }
                
                from src.models.api_models import SOPAdviceResponse
                response = SOPAdviceResponse(
                    session_id=self.state.session_id,
                    response=self.state.final_output,
                    is_question=False,
                    question_round=0,
                    max_rounds=2,
                    metadata={
                        "request_id": self.state.request_id,
                        "timestamp": datetime.utcnow().isoformat(),
                        "question_status": "completed",
                        "context_provided": self.state.context is not None,
                        "cache_hit": False
                    }
                )
                
                cache_manager.set(cache_key, response)
            
            return "sop_generated"
            
        except Exception as e:
            self.state.question_status = QuestionStatus.ERROR
            self.state.metadata["error"] = str(e)
            return "error"
    
    @listen("needs_more_info")
    def prepare_question_response(self):
        """准备问题响应"""
        # 这里可以添加额外的逻辑来处理问题响应
        return "question_ready"
    
    @listen("complete")
    def finalize_response(self):
        """最终化响应"""
        self.state.metadata["end_time"] = datetime.utcnow().isoformat()
        return "flow_completed"
    
    @listen("error")
    def handle_error(self):
        """处理错误"""
        self.state.metadata["end_time"] = datetime.utcnow().isoformat()
        return "flow_error"
    
    def get_response(self) -> Dict[str, Any]:
        """获取流程响应"""
        from src.models.api_models import SOPAdviceResponse
        
        if self.state.question_status == QuestionStatus.NEEDS_MORE_INFO:
            return {
                "response": self.state.requirements_output,
                "is_question": True,
                "question_round": self.state.question_round,
                "max_rounds": self.state.max_rounds,
                "status": "needs_more_info"
            }
        elif self.state.question_status == QuestionStatus.COMPLETED:
            return {
                "response": self.state.final_output,
                "is_question": False,
                "question_round": 0,
                "max_rounds": self.state.max_rounds,
                "status": "completed"
            }
        else:
            return {
                "response": f"处理失败: {self.state.metadata.get('error', '未知错误')}",
                "is_question": False,
                "question_round": 0,
                "max_rounds": self.state.max_rounds,
                "status": "error"
            }