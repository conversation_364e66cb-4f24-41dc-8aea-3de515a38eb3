"""
Custom embedder configuration to avoid OpenAI API conflicts.
This module provides a safe embedding function that doesn't interfere with our OpenRouter setup.
"""

import os
import logging
from typing import List, Optional
from sentence_transformers import SentenceTransformer
import numpy as np

logger = logging.getLogger(__name__)


class SafeHuggingFaceEmbedder:
    """
    A safe HuggingFace embedder that doesn't rely on external API calls
    and avoids URL protocol issues.
    """
    
    def __init__(self, model_name: str = "BAAI/bge-small-en-v1.5"):
        """
        Initialize the embedder with a local HuggingFace model.
        
        Args:
            model_name: The HuggingFace model to use for embeddings
        """
        self.model_name = model_name
        self._model = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the SentenceTransformer model."""
        try:
            logger.info(f"Loading embedding model: {self.model_name}")
            self._model = SentenceTransformer(self.model_name)
            logger.info("Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # Fallback to a smaller model if the primary one fails
            try:
                logger.info("Trying fallback model: all-MiniLM-L6-v2")
                self._model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("Fallback embedding model loaded successfully")
            except Exception as fallback_error:
                logger.error(f"Failed to load fallback model: {fallback_error}")
                raise RuntimeError("Could not initialize any embedding model")
    
    def __call__(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if self._model is None:
            self._initialize_model()
        
        try:
            # Clean the texts
            cleaned_texts = [text.replace('\n', ' ').strip() for text in texts]
            
            # Generate embeddings
            embeddings = self._model.encode(cleaned_texts, convert_to_numpy=True)
            
            # Convert to list format expected by ChromaDB
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            # Return zero embeddings as fallback
            embedding_dim = 384  # Default dimension for all-MiniLM-L6-v2
            return [[0.0] * embedding_dim for _ in texts]
    
    @property
    def dimension(self) -> int:
        """Get the embedding dimension."""
        if self._model is None:
            self._initialize_model()
        
        try:
            # Get dimension by encoding a test string
            test_embedding = self._model.encode(["test"])
            return len(test_embedding[0])
        except Exception:
            return 384  # Default dimension


def create_safe_embedder() -> SafeHuggingFaceEmbedder:
    """
    Create a safe embedder instance that avoids API conflicts.
    
    Returns:
        SafeHuggingFaceEmbedder instance
    """
    return SafeHuggingFaceEmbedder()


def get_embedder_config() -> dict:
    """
    Get embedder configuration that avoids OpenAI API conflicts.
    
    Returns:
        Dictionary with embedder configuration
    """
    return {
        "provider": "huggingface",
        "config": {
            "model": "BAAI/bge-small-en-v1.5",
            "trust_remote_code": True,
            "device": "cpu",  # Force CPU to avoid GPU issues
        }
    }


class NoOpEmbedder:
    """
    A no-operation embedder that returns dummy embeddings.
    Used as a fallback when all other embedding methods fail.
    """
    
    def __init__(self, dimension: int = 384):
        self.dimension = dimension
    
    def __call__(self, texts: List[str]) -> List[List[float]]:
        """Return dummy embeddings."""
        return [[0.1] * self.dimension for _ in texts]


def create_fallback_embedder() -> NoOpEmbedder:
    """
    Create a fallback embedder that always works.
    
    Returns:
        NoOpEmbedder instance
    """
    return NoOpEmbedder()


# Environment variable to disable problematic embedding features
def setup_safe_environment():
    """
    Set up environment variables to avoid embedding conflicts.
    """
    # Disable ChromaDB telemetry to avoid connection issues
    os.environ.setdefault("ANONYMIZED_TELEMETRY", "False")
    os.environ.setdefault("CHROMA_TELEMETRY_ANONYMOUS", "false")
    
    # Disable PostHog analytics to avoid URL protocol errors
    os.environ.setdefault("POSTHOG_ENABLED", "false")
    os.environ.setdefault("POSTHOG_HOST", "")
    
    # Disable CrewAI telemetry to avoid remote connections
    os.environ.setdefault("CREWAI_TELEMETRY_ENABLED", "false")
    os.environ.setdefault("CREWAI_DISABLE_TELEMETRY", "true")
    
    # Disable LangChain tracing to avoid remote connections
    os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
    os.environ.setdefault("LANGCHAIN_ENDPOINT", "")
    
    # Disable CrewAI memory external connections
    os.environ.setdefault("CREWAI_MEMORY_PROVIDER", "local")
    os.environ.setdefault("CREWAI_MEMORY_BACKEND", "local")
    
    # Disable vector database external connections
    os.environ.setdefault("VECTOR_DB_PROVIDER", "local")
    os.environ.setdefault("QDRANT_URL", "")
    os.environ.setdefault("CHROMA_URL", "")
    
    # Set a safe storage directory
    if not os.environ.get("CREWAI_STORAGE_DIR"):
        os.environ["CREWAI_STORAGE_DIR"] = "k8s-sop-agent-storage"
    
    logger.info("Safe embedding environment configured")


# Initialize safe environment when module is imported
setup_safe_environment()
