"""
Usage Examples for K8s SOP Agent Crews

This file demonstrates how to use the new crew structure with simplified calling patterns.
"""

import os
from dotenv import load_dotenv
from src.crews import BaseSopCrew, RequirementsCrew

# Load environment variables
load_dotenv()

def example_complete_sop_generation():
    """
    Example: Generate a complete SOP using BaseSopCrew
    
    This uses the full 4-agent workflow:
    1. Requirements collection
    2. Query rewriting  
    3. Knowledge retrieval
    4. Response synthesis
    """
    print("🚀 Complete SOP Generation Example")
    print("=" * 50)
    
    # Simple instantiation and execution
    inputs = {
        'question': '我需要写一个Kubernetes部署的SOP，包括Rolling Update策略',
        'question_context': '这是第一轮信息收集。'
    }
    
    # One-line execution - exactly what the user requested!
    result = BaseSopCrew(session_id="complete_example").crew().kickoff(inputs=inputs)
    
    print("📄 Generated SOP:")
    print(result.raw)
    print("=" * 50)
    return result

def example_requirements_only():
    """
    Example: Only collect requirements using RequirementsCrew
    
    This uses only the requirements advisor for initial information gathering.
    """
    print("📋 Requirements Collection Only Example")  
    print("=" * 50)
    
    inputs = {
        'question': '我想为生产环境创建一个数据库备份的SOP',
        'question_context': '这是第一轮信息收集。'
    }
    
    # Specialized crew for requirements only
    result = RequirementsCrew(session_id="requirements_example").crew().kickoff(inputs=inputs)
    
    print("📝 Requirements Analysis:")
    print(result.raw)
    print("=" * 50)
    return result

def example_session_management():
    """
    Example: Using different sessions for different projects
    """
    print("🗂️ Session Management Example")
    print("=" * 50)
    
    # Project A - Web Application Deployment
    project_a_inputs = {
        'question': 'Web应用的Kubernetes部署SOP',
        'question_context': '这是第一轮信息收集。'
    }
    
    project_a_result = BaseSopCrew(session_id="project_web_app").crew().kickoff(inputs=project_a_inputs)
    print(f"Project A Result Length: {len(project_a_result.raw)} characters")
    
    # Project B - Database Migration  
    project_b_inputs = {
        'question': '数据库迁移的操作程序',
        'question_context': '这是第一轮信息收集。'
    }
    
    project_b_result = BaseSopCrew(session_id="project_db_migration").crew().kickoff(inputs=project_b_inputs)
    print(f"Project B Result Length: {len(project_b_result.raw)} characters")
    
    print("✅ Different sessions maintain separate contexts")
    print("=" * 50)

def example_quick_prototyping():
    """
    Example: Quick prototyping and testing different approaches
    """
    print("⚡ Quick Prototyping Example")
    print("=" * 50)
    
    test_questions = [
        "Kubernetes节点维护的标准操作程序",
        "容器镜像安全扫描的工作流程", 
        "服务网格升级的最佳实践"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        
        # Quick one-liner for rapid prototyping
        result = RequirementsCrew(session_id=f"test_{i}").crew().kickoff(inputs={
            'question': question,
            'question_context': '快速原型测试。'
        })
        
        # Quick analysis
        is_asking_questions = '?' in result.raw or '？' in result.raw
        print(f"   Result type: {'❓ Asking for more info' if is_asking_questions else '📄 Generated content'}")
        print(f"   Length: {len(result.raw)} characters")

if __name__ == "__main__":
    """
    Run all examples to demonstrate the new crew usage patterns
    """
    
    try:
        print("🎯 K8s SOP Agent - New Crew Usage Examples")
        print("🔧 Simple calling pattern: CrewClass().crew().kickoff(inputs=inputs)")
        print("\n")
        
        # Run examples
        example_complete_sop_generation()
        print("\n")
        
        example_requirements_only()
        print("\n")
        
        example_session_management()  
        print("\n")
        
        example_quick_prototyping()
        
        print("\n🎉 All examples completed successfully!")
        print("💡 You can now use: BaseSopCrew().crew().kickoff(inputs=inputs)")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("Please ensure environment variables are set correctly.") 