"""
Crew工厂类 - 统一crew初始化逻辑
提供统一的接口来创建和管理不同类型的crew
"""

from typing import Dict, Any, Optional
from src.crews import BaseSopCrew, RequirementsCrew, FastSopCrew
from src.utils.llm_pool import llm_pool


class CrewFactory:
    """Crew工厂类，统一管理crew实例"""
    
    def __init__(self):
        self._crews: Dict[str, Any] = {}
        self._initialized = False
    
    def initialize(self):
        """初始化工厂"""
        if self._initialized:
            return
        
        # 预初始化LLM连接池
        llm_pool.initialize_pool()
        self._initialized = True
    
    def get_crew(self, crew_type: str = "base", session_id: Optional[str] = None):
        """
        获取指定类型的crew实例
        
        Args:
            crew_type: crew类型 (base, requirements, fast)
            session_id: 会话ID（可选）
            
        Returns:
            crew实例
        """
        if not self._initialized:
            self.initialize()
        
        crew_key = f"{crew_type}_{session_id}" if session_id else crew_type
        
        if crew_key not in self._crews:
            if crew_type == "requirements":
                self._crews[crew_key] = RequirementsCrew(session_id=session_id)
            elif crew_type == "fast":
                self._crews[crew_key] = FastSopCrew(session_id=session_id)
            else:
                self._crews[crew_key] = BaseSopCrew(session_id=session_id)
        
        return self._crews[crew_key]
    
    def get_base_sop_crew(self, session_id: Optional[str] = None):
        """获取BaseSopCrew实例"""
        return self.get_crew("base", session_id)
    
    def get_requirements_crew(self, session_id: Optional[str] = None):
        """获取RequirementsCrew实例"""
        return self.get_crew("requirements", session_id)
    
    def get_fast_sop_crew(self, session_id: Optional[str] = None):
        """获取FastSopCrew实例"""
        return self.get_crew("fast", session_id)
    
    def clear_crew(self, crew_type: str, session_id: Optional[str] = None):
        """清除指定的crew实例"""
        crew_key = f"{crew_type}_{session_id}" if session_id else crew_type
        if crew_key in self._crews:
            del self._crews[crew_key]
    
    def clear_all(self):
        """清除所有crew实例"""
        self._crews.clear()
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "initialized": self._initialized,
            "crew_count": len(self._crews),
            "crew_types": list(self._crews.keys())
        }


# 全局工厂实例
crew_factory = CrewFactory()