"""
LLM连接池实现 - 单例模式管理LLM连接
避免每次crew初始化都重新创建LLM实例
"""

import os
from typing import Optional, Dict, Any
from crewai import LLM
from threading import Lock
import logging

logger = logging.getLogger(__name__)


class LLMPool:
    """LLM连接池单例类"""
    
    _instance: Optional['LLMPool'] = None
    _lock: Lock = Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance
    
    def _initialize(self):
        """初始化连接池"""
        self._llm_instances: Dict[str, LLM] = {}
        self._default_llm: Optional[LLM] = None
        self._initialized = False
    
    def initialize_pool(self):
        """初始化连接池（延迟初始化）"""
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
            
            # 加载和验证LLM配置
            api_key = os.getenv("OPENAI_API_KEY")
            api_base = os.getenv("OPENAI_API_BASE")
            model_name = os.getenv("OPENAI_MODEL_NAME")

            if not all([api_key, api_base, model_name]):
                raise ValueError(
                    "Missing one or more required LLM environment variables: "
                    "OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL_NAME. "
                    "Please set them in your .env file."
                )

            if not api_base.startswith(("http://", "https://")):
                raise ValueError(
                    f"Invalid OPENAI_API_BASE: '{api_base}'. "
                    "It must be a valid URL (e.g., 'https://api.example.com/v1')."
                )

            # 创建默认LLM实例（启用流式）
            self._default_llm = LLM(
                model=f"openai/{model_name}",
                base_url=api_base,
                api_key=api_key,
                temperature=0.2,
                timeout=60,
                stream=True  # 启用流式响应
            )
            
            # 创建快速模式LLM实例（启用流式）
            self._llm_instances["fast"] = LLM(
                model=f"openai/{model_name}",
                base_url=api_base,
                api_key=api_key,
                temperature=0.1,
                timeout=30,
                stream=True  # 启用流式响应
            )
            
            # 创建需求收集LLM实例（启用流式）
            self._llm_instances["requirements"] = LLM(
                model=f"openai/{model_name}",
                base_url=api_base,
                api_key=api_key,
                temperature=0.1,
                timeout=30,
                stream=True  # 启用流式响应
            )
            
            self._initialized = True
            logger.info("LLM连接池初始化完成")
    
    def get_llm(self, llm_type: str = "default") -> LLM:
        """
        获取指定类型的LLM实例
        
        Args:
            llm_type: LLM类型 (default, fast, requirements)
            
        Returns:
            LLM实例
        """
        if not self._initialized:
            self.initialize_pool()
        
        if llm_type == "default":
            return self._default_llm
        
        return self._llm_instances.get(llm_type, self._default_llm)
    
    def get_default_llm(self) -> LLM:
        """获取默认LLM实例"""
        return self.get_llm("default")
    
    def get_fast_llm(self) -> LLM:
        """获取快速模式LLM实例"""
        return self.get_llm("fast")
    
    def get_requirements_llm(self) -> LLM:
        """获取需求收集LLM实例"""
        return self.get_llm("requirements")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "initialized": self._initialized,
            "llm_count": len(self._llm_instances) + (1 if self._default_llm else 0),
            "llm_types": ["default"] + list(self._llm_instances.keys())
        }


# 全局单例实例
llm_pool = LLMPool()