"""
流式输出工具 - 提供真正的LLM响应流式输出功能
"""

import sys
import time
from typing import Callable, Any, List, Dict
from crewai.utilities import logger

class StreamingCallbackHandler:
    """真正的流式回调处理器 - 实时显示LLM响应内容"""
    
    def __init__(self, prefix: str = "🤖"):
        self.prefix = prefix
        self.current_content = ""
        self.is_streaming = False
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM开始生成时调用"""
        self.is_streaming = True
        self.current_content = ""
        print(f"\n{self.prefix} ", end="", flush=True)
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """收到新token时调用"""
        if self.is_streaming:
            self.current_content += token
            # 实时输出token
            print(token, end="", flush=True)
    
    def on_llm_end(self, response, **kwargs) -> None:
        """LLM生成完成时调用"""
        self.is_streaming = False
        print("\n", end="", flush=True)
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """LLM错误时调用"""
        self.is_streaming = False
        print(f"\n❌ LLM错误: {error}", flush=True)
    
    def get_content(self) -> str:
        """获取完整的响应内容"""
        return self.current_content

class StreamingOutput:
    """流式输出管理器"""
    
    def __init__(self):
        self.callback_handler = StreamingCallbackHandler()
    
    def get_callbacks(self) -> List[StreamingCallbackHandler]:
        """获取流式回调处理器列表"""
        return [self.callback_handler]
    
    def print_step(self, message: str, icon: str = "🔍"):
        """打印处理步骤"""
        print(f"\n{icon} {message}")
    
    def print_success(self, message: str):
        """打印成功消息"""
        print(f"✅ {message}")
    
    def print_error(self, message: str):
        """打印错误消息"""
        print(f"❌ {message}")

# 全局流式输出实例
streaming_output = StreamingOutput()

def with_streaming(func: Callable) -> Callable:
    """
    装饰器：为函数添加流式输出功能
    
    Args:
        func: 需要添加流式输出的函数
        
    Returns:
        包装后的函数
    """
    def wrapper(*args, **kwargs):
        # 获取函数名作为默认消息
        func_name = func.__name__.replace('_', ' ').title()
        
        streaming_output.print_step(f"开始 {func_name}")
        
        try:
            result = func(*args, **kwargs)
            streaming_output.print_success(f"{func_name} 完成")
            return result
        except Exception as e:
            streaming_output.print_error(f"{func_name} 失败: {str(e)}")
            raise
    
    return wrapper

def stream_execute(crew_func: Callable, inputs: dict, message: str = "处理中") -> Any:
    """
    执行Crew函数并显示流式进度
    
    Args:
        crew_func: Crew的kickoff函数
        inputs: 输入参数
        message: 显示消息
        
    Returns:
        执行结果
    """
    streaming_output.print_step(message)
    
    try:
        result = crew_func(inputs=inputs)
        streaming_output.print_success(f"{message} 完成")
        return result
    except Exception as e:
        streaming_output.print_error(f"{message} 失败: {str(e)}")
        raise