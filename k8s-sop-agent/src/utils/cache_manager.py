"""
缓存管理器 - 提供简单的请求缓存功能
"""

import hashlib
import json
from typing import Any, Dict, Optional
from datetime import datetime, timedelta
from threading import Lock
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """简单的内存缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认缓存时间（秒）
        """
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._lock = Lock()
        self._hits = 0
        self._misses = 0
    
    def _generate_key(self, data: Any) -> str:
        """生成缓存键"""
        if isinstance(data, str):
            data_str = data
        else:
            data_str = json.dumps(data, sort_keys=True)
        
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def get(self, key_data: Any) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key_data: 用于生成缓存键的数据
            
        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        cache_key = self._generate_key(key_data)
        
        with self._lock:
            if cache_key in self._cache:
                cache_item = self._cache[cache_key]
                
                # 检查是否过期
                if datetime.now() < cache_item['expires_at']:
                    self._hits += 1
                    return cache_item['value']
                else:
                    # 过期，删除
                    del self._cache[cache_key]
            
            self._misses += 1
            return None
    
    def set(self, key_data: Any, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存值
        
        Args:
            key_data: 用于生成缓存键的数据
            value: 要缓存的值
            ttl: 缓存时间（秒），None使用默认值
        """
        if ttl is None:
            ttl = self._default_ttl
        
        cache_key = self._generate_key(key_data)
        
        with self._lock:
            # 检查缓存大小，如果超过最大大小，删除最旧的条目
            if len(self._cache) >= self._max_size:
                # 找到最旧的条目
                oldest_key = None
                oldest_time = datetime.max
                
                for key, item in self._cache.items():
                    if item['created_at'] < oldest_time:
                        oldest_time = item['created_at']
                        oldest_key = key
                
                if oldest_key:
                    del self._cache[oldest_key]
            
            self._cache[cache_key] = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(seconds=ttl)
            }
    
    def delete(self, key_data: Any) -> None:
        """删除缓存值"""
        cache_key = self._generate_key(key_data)
        
        with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'size': len(self._cache),
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0,
                'max_size': self._max_size,
                'default_ttl': self._default_ttl
            }
    
    def cleanup(self) -> int:
        """清理过期缓存，返回清理的数量"""
        cleaned = 0
        now = datetime.now()
        
        with self._lock:
            keys_to_delete = []
            
            for key, item in self._cache.items():
                if now >= item['expires_at']:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self._cache[key]
                cleaned += 1
        
        return cleaned


# 全局缓存实例
cache_manager = CacheManager(max_size=500, default_ttl=600)  # 10分钟默认缓存时间