"""
Rerank 模型配置文件
配置 cross-encoder/ms-marco-MiniLM-L4-v2 模型的相关参数
"""

import os
import torch

# Rerank 模型配置
RERANK_MODEL_NAME = "cross-encoder/ms-marco-MiniLM-L4-v2"

# 检索参数
DEFAULT_TOP_K = 20  # 初始检索的文档数量（更多文档用于 rerank）
DEFAULT_RERANK_TOP_K = 5  # rerank 后返回的最终文档数量

# 模型缓存和设备配置
RERANK_MODEL_CACHE_DIR = os.path.join(os.getcwd(), ".cache", "rerank_models")
RERANK_MODEL_DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

# 分数阈值和归一化
MIN_RERANK_SCORE = -10.0  # 最低 rerank 分数阈值
SCORE_NORMALIZATION = True  # 是否对分数进行归一化

# 性能优化配置
RERANK_BATCH_SIZE = 32  # rerank 时的批处理大小
MAX_SEQUENCE_LENGTH = 512  # 最大序列长度

# 调试和日志配置
ENABLE_RERANK_DEBUG = os.getenv("RERANK_DEBUG", "false").lower() == "true"
RERANK_LOG_LEVEL = os.getenv("RERANK_LOG_LEVEL", "INFO")

print(f"Rerank configuration loaded:")
print(f"  Model: {RERANK_MODEL_NAME}")
print(f"  Device: {RERANK_MODEL_DEVICE}")
print(f"  Cache dir: {RERANK_MODEL_CACHE_DIR}")
print(f"  Initial retrieval: {DEFAULT_TOP_K} docs")
print(f"  Final results: {DEFAULT_RERANK_TOP_K} docs")