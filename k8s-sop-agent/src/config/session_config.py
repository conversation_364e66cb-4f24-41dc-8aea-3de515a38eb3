"""
Session configuration for WebSessionManager
"""

import os
from typing import Optional
from dataclasses import dataclass
from src.session_manager import StorageBackend


@dataclass
class SessionConfig:
    """Configuration for WebSessionManager"""
    
    # Storage backend configuration
    storage_backend: StorageBackend = StorageBackend.MEMORY
    redis_url: str = "redis://localhost:6379"
    
    # Session management configuration
    session_timeout_hours: int = 24
    max_sessions_per_user: int = 10
    
    # Cleanup configuration
    cleanup_interval_minutes: int = 60
    
    @classmethod
    def from_env(cls) -> 'SessionConfig':
        """Create configuration from environment variables"""
        return cls(
            storage_backend=StorageBackend(os.getenv("SESSION_STORAGE_BACKEND", "memory")),
            redis_url=os.getenv("SESSION_REDIS_URL", "redis://localhost:6379"),
            session_timeout_hours=int(os.getenv("SESSION_TIMEOUT_HOURS", "24")),
            max_sessions_per_user=int(os.getenv("SESSION_MAX_SESSIONS_PER_USER", "10")),
            cleanup_interval_minutes=int(os.getenv("SESSION_CLEANUP_INTERVAL_MINUTES", "60"))
        )


def get_session_config() -> SessionConfig:
    """Get session configuration instance"""
    return SessionConfig.from_env()