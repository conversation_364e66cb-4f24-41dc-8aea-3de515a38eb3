# Kubernetes 回退策略

## 快速回退

### 使用 kubectl rollout 命令
```bash
# 查看部署历史
kubectl rollout history deployment/my-app

# 回退到上一个版本
kubectl rollout undo deployment/my-app

# 回退到指定版本
kubectl rollout undo deployment/my-app --to-revision=2

# 检查回退状态
kubectl rollout status deployment/my-app
```

## 自动回退配置

### 配置回退策略
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  revisionHistoryLimit: 10  # 保留的历史版本数量
  progressDeadlineSeconds: 600  # 部署超时时间
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
```

## 回退触发条件

### 基于健康检查的自动回退
- 连续健康检查失败
- 错误率超过阈值
- 响应时间超过预期

### 监控指标触发回退
```yaml
# 示例：基于错误率的回退规则
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
  for: 2m
  labels:
    severity: critical
    action: rollback
  annotations:
    summary: High error rate detected
    description: "Error rate is {{ $value }} for the last 5 minutes"
```

## 回退验证

### 回退后验证步骤
1. 检查 Pod 状态
2. 验证服务可用性
3. 检查关键业务指标
4. 确认用户体验正常

```bash
# 验证脚本示例
#!/bin/bash
DEPLOYMENT_NAME="my-app"
NAMESPACE="default"

# 等待回退完成
kubectl rollout status deployment/$DEPLOYMENT_NAME -n $NAMESPACE

# 检查 Pod 状态
kubectl get pods -l app=$DEPLOYMENT_NAME -n $NAMESPACE

# 健康检查
curl -f http://my-app-service/health || exit 1

echo "Rollback verification completed successfully"
```