# Kubernetes Deployment 最佳实践

## 滚动更新策略

Kubernetes 支持多种部署策略，其中滚动更新（Rolling Update）是最常用的策略：

### 配置滚动更新
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  replicas: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  template:
    spec:
      containers:
      - name: app
        image: my-app:v2
```

### 关键参数说明
- `maxUnavailable`: 更新过程中不可用的 Pod 数量上限
- `maxSurge`: 更新过程中可以创建的额外 Pod 数量上限

## 健康检查配置

配置适当的健康检查确保应用正常运行：

```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /ready
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
```

## 资源限制

设置合适的资源请求和限制：

```yaml
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```