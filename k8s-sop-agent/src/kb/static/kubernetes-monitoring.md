# Kubernetes 监控和告警

## 监控指标

### 核心指标
- CPU 使用率
- 内存使用率
- 网络 I/O
- 磁盘 I/O
- Pod 状态

### 应用指标
- 请求延迟
- 错误率
- 吞吐量
- 业务指标

## 告警配置

### Prometheus 告警规则
```yaml
groups:
- name: kubernetes-apps
  rules:
  - alert: KubePodCrashLooping
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: Kubernetes pod crash looping
      description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping"

  - alert: KubePodNotReady
    expr: kube_pod_status_phase{phase=~"Pending|Unknown"} > 0
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: Kubernetes Pod not ready
      description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} has been in a non-ready state for longer than 15 minutes."
```

## 告警屏蔽策略

在维护期间需要屏蔽相关告警：

### 使用 Alertmanager 静默功能
```bash
# 创建静默规则
amtool silence add alertname="KubePodNotReady" \
  --duration="2h" \
  --comment="Maintenance window for deployment"

# 查看活动的静默规则
amtool silence query

# 删除静默规则
amtool silence expire <silence-id>
```

### 基于标签的告警屏蔽
```yaml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      service: maintenance
    receiver: 'null'
```