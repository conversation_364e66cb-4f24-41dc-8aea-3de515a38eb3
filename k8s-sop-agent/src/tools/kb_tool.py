import os
from typing import List
from crewai.tools import BaseTool
from qdrant_client import QdrantClient
from langchain_community.embeddings import FastEmbedEmbeddings
from langchain_qdrant import QdrantVectorStore
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

class KnowledgeBaseTool(BaseTool):
    name: str = "KnowledgeBaseTool"
    description: str = "A tool to query the Kubernetes SOP knowledge base. Use it to get information on K8s best practices or to understand the context of a user's SOP."

    # Configuration for document limits
    max_chars_per_doc: int = int(os.getenv("KB_TOOL_MAX_CHARS_PER_DOC", 1000))
    max_docs: int = int(os.getenv("KB_TOOL_MAX_DOCS", 3))
    default_top_k: int = 5

    _client: QdrantClient = None
    _embedding_function = None
    _text_splitter = None
    _vector_stores = {}  # Store vector stores for different collections
    _static_kb_path: str = "src/kb/static"
    _dynamic_kb_path: str = "src/kb/dynamic"
    _persistent_storage_path: str = "data/vector_storage"
    _initialized: bool = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self._initialized:
            # 延迟初始化，避免在工具验证时构建知识库
            self._initialized = True

    def _build_kb(self):
        """Builds the knowledge base from static and dynamic sources."""
        print("Building knowledge base...")
        
        # 检查集合是否已存在，避免重复构建
        existing_collections = [coll.name for coll in self._client.get_collections().collections]
        
        if "static_kb" not in existing_collections:
            self._load_and_ingest(self._static_kb_path, "static_kb")
        else:
            print("静态知识库已存在，跳过重建")
            self._load_existing_collection("static_kb")
        
        if "dynamic_kb" not in existing_collections:
            self._load_and_ingest(self._dynamic_kb_path, "dynamic_kb")
        else:
            print("动态知识库已存在，跳过重建")
            self._load_existing_collection("dynamic_kb")
        
        print("Knowledge base built successfully.")

    def _load_and_ingest(self, path: str, collection_name: str):
        """Loads documents and ingests them into a Qdrant collection."""
        # Ensure dummy files exist for initialization
        if not os.path.exists(path):
            os.makedirs(path)

        # Create a dummy file if the directory is empty
        if not any(os.scandir(path)):
            with open(os.path.join(path, "placeholder.md"), "w") as f:
                f.write("This is a placeholder document.")

        # Using a simple loop instead of DirectoryLoader to avoid extra dependencies
        docs = []
        for filename in os.listdir(path):
            if filename.endswith(".md"):
                loader = TextLoader(os.path.join(path, filename))
                docs.extend(loader.load())

        if not docs:
            print(f"No documents found in {path} for collection {collection_name}.")
            return

        split_docs = self._text_splitter.split_documents(docs)

        # Create and store the vector store for this collection
        vector_store = QdrantVectorStore.from_documents(
            split_docs,
            embedding=self._embedding_function,
            client=self._client,
            collection_name=collection_name,
            force_recreate=True,
        )
        
        # Store the vector store for later querying
        self._vector_stores[collection_name] = vector_store
        print(f"Successfully ingested {len(split_docs)} chunks into '{collection_name}'.")
    
    def _load_existing_collection(self, collection_name: str):
        """加载已存在的集合"""
        vector_store = QdrantVectorStore(
            client=self._client,
            collection_name=collection_name,
            embeddings=self._embedding_function,
        )
        self._vector_stores[collection_name] = vector_store
        print(f"Loaded existing collection '{collection_name}' from persistent storage.")

    def _run(self, query: str, collection_name: str = None, top_k: int = None) -> str:
        """
        使用基础RAG查询知识库
        
        Args:
            query: 查询文本
            collection_name: 集合名称 ('static_kb' 或 'dynamic_kb')。如果未提供或无效，将使用默认集合。
            top_k: 检索的文档数量
            
        Returns:
            格式化的检索结果
        """
        try:
            if top_k is None:
                top_k = self.default_top_k
                
            # 智能选择集合
            available_collections = list(self._vector_stores.keys())
            if not available_collections:
                return "Error: No knowledge base collections are available."

            if collection_name not in self._vector_stores:
                print(f"Warning: Collection '{collection_name}' not found or not provided. Falling back to default collection 'static_kb'.")
                collection_name = "static_kb" # 默认使用静态知识库
                if collection_name not in available_collections:
                    collection_name = available_collections[0] # 如果默认的也不在，则使用第一个可用的

            vector_store = self._vector_stores[collection_name]
            
            # 向量相似度检索
            print(f"Performing retrieval for query: '{query[:50]}...'")
            retriever = vector_store.as_retriever(search_kwargs={"k": top_k})
            docs = retriever.get_relevant_documents(query)
            
            if not docs:
                return f"No relevant documents found for query: {query}"
            
            # 限制文档数量
            final_docs = docs[:self.max_docs]
            
            # 格式化结果
            result_parts = [
                f"Found {len(final_docs)} relevant documents for query: '{query}'\n",
                f"Collection: {collection_name}",
                "\n" + "="*60 + "\n"
            ]
            
            for i, doc in enumerate(final_docs, 1):
                content = doc.page_content
                metadata = doc.metadata
                
                # 截断内容避免过长
                truncated_content = content[:self.max_chars_per_doc] + "..." if len(content) > self.max_chars_per_doc else content
                
                result_parts.append(f"Document {i}:")
                result_parts.append("-" * 40)
                result_parts.append(f"Content: {truncated_content}")
                if metadata:
                    result_parts.append(f"Metadata: {metadata}")
                result_parts.append("\n" + "="*60 + "\n")
            
            return "\n".join(result_parts)
            
        except Exception as e:
            print(f"Error querying knowledge base: {e}")
            import traceback
            traceback.print_exc()
            return f"Error occurred while querying knowledge base: {str(e)}"
