"""
Flow版本的CLI入口 - 使用CrewAI Flows模式
"""

import os
from dotenv import load_dotenv
from datetime import datetime
import re

from src.flows.sop_advice_flow import SopAdviceFlow
from src.models.api_models import SOPAdviceResponse

# Load environment variables from .env file
load_dotenv()

def create_enhanced_input_system():
    """创建增强的输入系统"""
    from prompt_toolkit import prompt
    from prompt_toolkit.completion import WordCompleter
    from prompt_toolkit.history import InMemoryHistory
    from prompt_toolkit.styles import Style
    from prompt_toolkit.formatted_text import HTML
    from prompt_toolkit.key_binding import KeyBindings

    # 创建命令历史记录
    history = InMemoryHistory()

    # 创建自动补全词汇
    k8s_keywords = [
        "Pod", "Deployment", "Service", "ConfigMap", "Secret", "Namespace",
        "Ingress", "PersistentVolume", "PersistentVolumeClaim", "StorageClass",
        "StatefulSet", "DaemonSet", "Job", "CronJob", "ReplicaSet",
        "部署", "创建", "删除", "更新", "扩容", "缩容", "重启", "回滚",
        "升级", "降级", "迁移", "备份", "恢复", "监控", "调试",
        "生成SOP", "创建文档", "部署应用", "配置服务", "设置监控",
        "故障排查", "性能优化", "安全配置", "网络配置", "存储配置",
        "生产环境", "测试环境", "开发环境", "预发布环境",
        "快速生成", "直接生成", "简单配置", "详细文档", "完整流程"
    ]

    completer = WordCompleter(k8s_keywords, ignore_case=True)

    # 创建样式
    style = Style.from_dict({
        'prompt': '#00aa00 bold',
        'input': '#ffffff',
        'completion-menu.completion': '#444444 bg:#ffffff',
        'completion-menu.completion.current': '#ffffff bg:#444444',
        'bottom-toolbar': '#444444 bg:#bbbbbb',
    })

    # 创建键绑定
    bindings = KeyBindings()

    @bindings.add('c-c')
    def _(event):
        event.app.exit(exception=KeyboardInterrupt)

    @bindings.add('c-d')
    def _(event):
        event.app.exit()

    return {
        'history': history,
        'completer': completer,
        'style': style,
        'key_bindings': bindings
    }

def get_user_input(prompt_text: str, input_system: dict) -> str:
    """获取用户输入"""
    try:
        from prompt_toolkit import prompt
        from prompt_toolkit.formatted_text import HTML

        def get_bottom_toolbar():
            return HTML(
                '💡 <b>提示:</b> '
                'Tab键自动补全 | '
                '↑↓键浏览历史 | '
                'Ctrl+C退出 | '
                '支持中文输入'
            )

        user_input = prompt(
            HTML(f'<prompt>{prompt_text}</prompt>'),
            history=input_system['history'],
            completer=input_system['completer'],
            style=input_system['style'],
            key_bindings=input_system['key_bindings'],
            bottom_toolbar=get_bottom_toolbar,
            complete_style='multi-column',
            mouse_support=True,
            wrap_lines=True,
            multiline=False,
            vi_mode=False,
            enable_history_search=True,
            search_ignore_case=True,
        )

        return user_input.strip()

    except KeyboardInterrupt:
        raise KeyboardInterrupt
    except EOFError:
        return "exit"
    except Exception as e:
        print(f"⚠️ 输入系统错误: {e}")
        return input(prompt_text).strip()

def show_help():
    """显示帮助信息"""
    help_text = """
📚 K8s SOP文档生成器 (Flow模式) - 帮助信息
═══════════════════════════════════════════════════════════════

🎯 主要功能:
  • 使用CrewAI Flows模式生成Kubernetes SOP文档
  • 内置状态管理和可视化工作流
  • 支持多轮对话和智能条件路由

💡 使用技巧:
  • 描述要操作的K8s资源类型和操作目的
  • 系统会自动判断是否需要更多信息
  • 支持中文输入和自动补全

🔧 特殊命令:
  • help/帮助/?     - 显示此帮助信息
  • clear/清屏      - 清屏
  • status/状态     - 显示当前会话状态
  • exit/quit/退出  - 退出程序

⌨️ 快捷键:
  • Tab键          - 自动补全K8s相关词汇
  • ↑↓键          - 浏览输入历史
  • Ctrl+C         - 退出程序

📝 示例输入:
  • "生成Pod部署SOP"
  • "创建Service配置文档" 
  • "Deployment滚动更新流程"
  • "故障排查指南"

═══════════════════════════════════════════════════════════════
"""
    print(help_text)

def format_flow_output(result: dict, flow: SopAdviceFlow) -> str:
    """格式化Flow输出结果"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    header = f"""
{'='*80}
📋 Flow模式SOP文档生成结果 | {timestamp}
📁 会话ID: {flow.state.session_id} | 状态: {flow.state.question_status}
{'='*80}
"""

    footer = f"""
{'='*80}
💡 提示: 使用Flow模式，支持状态管理和可视化工作流
{'='*80}
"""

    return header + result["response"] + footer

def main_flow():
    """Flow版本的CLI主函数"""
    print("📄 K8s SOP文档生成器 (Flow模式)")
    print("=" * 60)
    print("🚀 使用CrewAI Flows模式，支持状态管理和可视化工作流")
    print("💬 输入您的需求，系统将自动处理")
    print("=" * 60)

    try:
        # 创建增强输入系统
        print("\n🔧 初始化增强输入系统...")
        input_system = create_enhanced_input_system()
        print("✅ 输入系统就绪")

        print("\n💡 使用说明:")
        print("  • Tab键: 自动补全K8s相关词汇")
        print("  • ↑↓键: 浏览输入历史") 
        print("  • Ctrl+C: 退出程序")
        print("  • 输入 'exit', 'quit', '退出': 正常退出")
        print("-" * 60)
        
        conversation_count = 0
        
        while True:
            try:
                conversation_count += 1
                prompt_text = f"[{conversation_count}] 您的需求: "
                user_input = get_user_input(prompt_text, input_system)

                # 处理退出命令
                if user_input.lower() in ["exit", "quit", "退出", "结束", ""]:
                    print("👋 感谢使用K8s SOP文档生成器，再见！")
                    break

                # 处理特殊命令
                if user_input.lower() in ["help", "帮助", "?"]:
                    show_help()
                    continue
                elif user_input.lower() in ["clear", "清屏"]:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    continue
                elif user_input.lower() in ["status", "状态"]:
                    print("📊 Flow模式 - 每次请求都是独立的流程执行")
                    continue

                # 跳过空输入
                if not user_input.strip():
                    continue

                # 使用Flow模式处理请求
                print("\n🚀 启动Flow工作流...")
                flow = SopAdviceFlow()
                flow.state.question = user_input
                
                # 执行Flow
                flow.kickoff()
                
                # 获取结果
                result = flow.get_response()
                
                # 格式化并显示结果
                formatted_result = format_flow_output(result, flow)
                print(formatted_result)
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                print("请重试或输入 'exit' 退出")
    
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请检查配置和环境变量")

if __name__ == "__main__":
    main_flow()