# WebSessionManager Documentation

## Overview

The `WebSessionManager` extends the existing `SessionManager` to provide async support and Redis storage backend for Web API applications. It maintains backward compatibility while adding new features required for web-based deployments.

## Features

- **Async Operations**: All operations are async-compatible for use with FastAPI and other async web frameworks
- **Multiple Storage Backends**: Support for both in-memory and Redis storage
- **Session Limits**: Configurable per-user session limits
- **Automatic Cleanup**: Periodic cleanup of expired sessions
- **Session Timeout**: Configurable session timeout with automatic expiration
- **User Management**: Optional user-based session grouping and management

## Basic Usage

### Memory Backend (Default)

```python
from src.session_manager import WebSessionManager, StorageBackend

# Create manager with memory backend
manager = WebSessionManager(storage_backend=StorageBackend.MEMORY)
await manager.initialize()

# Create a session
session_id = await manager.create_session_async(user_id="user123")

# Get SOP crew for processing
sop_crew = await manager.get_sop_crew_async(session_id)

# Process requests...
await manager.update_session_activity(session_id)

# Cleanup
await manager.close()
```

### Redis Backend

```python
from src.session_manager import WebSessionManager, StorageBackend

# Create manager with Redis backend
manager = WebSessionManager(
    storage_backend=StorageBackend.REDIS,
    redis_url="redis://localhost:6379"
)
await manager.initialize()

# Same API as memory backend
session_id = await manager.create_session_async(user_id="user123")
# ... rest of operations are identical
```

### Configuration-Based Setup

```python
from src.session_manager import WebSessionManager
from src.config.session_config import get_session_config

# Load configuration from environment
config = get_session_config()
manager = WebSessionManager(
    storage_backend=config.storage_backend,
    redis_url=config.redis_url,
    session_timeout_hours=config.session_timeout_hours,
    max_sessions_per_user=config.max_sessions_per_user
)
```

## Configuration

### Environment Variables

Set these environment variables to configure the WebSessionManager:

```bash
# Storage backend (memory or redis)
SESSION_STORAGE_BACKEND=memory

# Redis connection URL (if using Redis backend)
SESSION_REDIS_URL=redis://localhost:6379

# Session timeout in hours
SESSION_TIMEOUT_HOURS=24

# Maximum sessions per user
SESSION_MAX_SESSIONS_PER_USER=10

# Cleanup interval in minutes
SESSION_CLEANUP_INTERVAL_MINUTES=60
```

### Programmatic Configuration

```python
manager = WebSessionManager(
    storage_backend=StorageBackend.REDIS,
    redis_url="redis://localhost:6379",
    session_timeout_hours=24,
    max_sessions_per_user=10
)
```

## API Reference

### Core Methods

#### `create_session_async(session_id=None, user_id=None) -> str`
Create a new session with optional custom ID and user association.

#### `get_session_async(session_id: str) -> Dict`
Retrieve session information and metadata.

#### `get_sop_crew_async(session_id: str) -> K8sSopCrew`
Get the SOP crew instance for processing requests.

#### `update_session_activity(session_id: str) -> None`
Update session last activity timestamp and increment message count.

#### `list_sessions_async(user_id=None) -> List[Dict]`
List all sessions, optionally filtered by user ID.

#### `reset_session_memory_async(session_id: str) -> None`
Reset session memory and conversation history.

#### `delete_session_async(session_id: str) -> None`
Delete a session and clean up associated resources.

#### `cleanup_expired_sessions_async() -> int`
Manually trigger cleanup of expired sessions. Returns count of cleaned sessions.

### Lifecycle Methods

#### `initialize() -> None`
Initialize the session manager (connect to Redis if needed, start cleanup task).

#### `close() -> None`
Close connections and cleanup background tasks.

## Storage Backends

### Memory Backend
- **Pros**: Fast, no external dependencies, simple setup
- **Cons**: Sessions lost on restart, not suitable for multi-instance deployments
- **Use Case**: Development, single-instance deployments, testing

### Redis Backend
- **Pros**: Persistent, supports multi-instance deployments, automatic expiration
- **Cons**: Requires Redis server, additional complexity
- **Use Case**: Production deployments, multi-instance setups, high availability

## Session Lifecycle

1. **Creation**: Session created with unique ID and optional user association
2. **Active**: Session processes requests, activity tracked automatically
3. **Timeout**: Session expires after configured timeout period
4. **Cleanup**: Expired sessions automatically cleaned up by background task
5. **Deletion**: Manual deletion or automatic cleanup removes all session data

## Error Handling

The WebSessionManager raises specific exceptions for different error conditions:

- `ValueError`: Session not found, session limit exceeded, invalid parameters
- `ImportError`: Redis not available when trying to use Redis backend
- `ConnectionError`: Redis connection issues

## Best Practices

1. **Always call `initialize()`** before using the manager
2. **Always call `close()`** when shutting down to cleanup resources
3. **Use try/finally blocks** or context managers to ensure cleanup
4. **Monitor session counts** to prevent memory/storage bloat
5. **Configure appropriate timeouts** based on your use case
6. **Use Redis backend** for production deployments
7. **Set reasonable session limits** to prevent abuse

## Integration with FastAPI

```python
from fastapi import FastAPI, HTTPException
from src.session_manager import WebSessionManager, StorageBackend

app = FastAPI()
session_manager = WebSessionManager(storage_backend=StorageBackend.REDIS)

@app.on_event("startup")
async def startup():
    await session_manager.initialize()

@app.on_event("shutdown")
async def shutdown():
    await session_manager.close()

@app.post("/api/v1/sessions")
async def create_session(user_id: str = None):
    try:
        session_id = await session_manager.create_session_async(user_id=user_id)
        return {"session_id": session_id}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/sop-advice")
async def get_sop_advice(session_id: str, question: str):
    try:
        sop_crew = await session_manager.get_sop_crew_async(session_id)
        # Process with sop_crew...
        await session_manager.update_session_activity(session_id)
        return {"response": "..."}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
```

## Monitoring and Maintenance

- Monitor session counts and memory usage
- Set up alerts for Redis connectivity issues
- Regularly check cleanup task performance
- Monitor session creation/deletion rates
- Consider implementing session analytics for usage patterns