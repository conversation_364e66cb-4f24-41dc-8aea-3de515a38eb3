# Project Structure

## Root Directory Organization
```
k8s-sop-agent/
├── config/                 # Agent and task configurations
├── src/                    # Main source code
├── .kiro/                  # Kiro IDE configuration and specs
├── .venv/                  # Python virtual environment
├── pyproject.toml          # Project dependencies and metadata
├── .env / .env.example     # Environment configuration
└── sample_sop.md           # Sample SOP for testing
```

## Configuration (`config/`)
- `agents.yaml` - Agent definitions (roles, goals, backstories)
- `tasks.yaml` - Task definitions and expected outputs
- **Pattern**: YAML-based configuration for CrewAI agents and tasks

## Source Code (`src/`)

### Main Entry Points
- `main.py` - CLI interface with interactive session management
- `web_main.py` - FastAPI web server entry point
- `crew.py` - Core CrewAI crew orchestration with flow control

### Core Components
- `session_manager.py` - Session management and memory isolation
- `custom_embedder.py` - Embedding configuration and setup

### Specialized Modules
- `config/rerank_config.py` - Cross-encoder reranking model configuration
- `models/api_models.py` - Pydantic models for API requests/responses
- `tools/kb_tool.py` - Enhanced RAG knowledge base tool with reranking
- `agents/` - Custom agent implementations (currently empty, uses config)
- `tasks/` - Custom task implementations (currently empty, uses config)

### Knowledge Base (`src/kb/`)
- `static/` - Static Kubernetes documentation and best practices
- `dynamic/` - Runtime user-provided SOPs and documents
- **Pattern**: Markdown files processed into vector embeddings

## Kiro IDE Configuration (`.kiro/`)
- `steering/` - AI assistant guidance rules
- `specs/` - Feature specifications and design documents
- `settings/` - IDE-specific configuration files

## Key Architectural Patterns

### Multi-Agent Flow
1. **Sequential Processing**: Agents execute in defined order with context passing
2. **Memory Isolation**: Each session maintains separate conversation context
3. **Flow Control**: Intelligent question rounds with configurable limits

### RAG Pipeline
1. **Document Ingestion**: Markdown files → Text chunks → Vector embeddings
2. **Retrieval**: Vector similarity search in Qdrant collections
3. **Reranking**: Cross-encoder model improves relevance scoring
4. **Synthesis**: Combined context for final response generation

### Configuration Management
- **Environment Variables**: `.env` for secrets and API keys
- **YAML Configuration**: `config/` for agent behaviors and tasks
- **Python Configuration**: `src/config/` for model parameters

### Testing Structure
- Test files in root directory with descriptive names
- Pattern: `test_*.py` for different component testing
- Integration tests for end-to-end workflows

## File Naming Conventions
- **Python modules**: `snake_case.py`
- **Configuration files**: `lowercase.yaml`
- **Documentation**: `kebab-case.md`
- **Test files**: `test_component_name.py`

## Import Patterns
- **Relative imports**: Use `from src.module import Class` from project root
- **Absolute paths**: Configuration files use `os.path.join()` for cross-platform compatibility
- **Tool imports**: Custom tools imported in `crew.py` for agent assignment