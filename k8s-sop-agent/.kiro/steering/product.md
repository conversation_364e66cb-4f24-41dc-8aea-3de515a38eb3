# Product Overview

## K8s SOP Agent

An AI-powered system for collaboratively writing expert-level Kubernetes Standard Operating Procedures (SOPs) using multi-agent orchestration.

### Core Purpose
- Generate professional, enterprise-grade Kubernetes SOPs through intelligent conversation
- Provide proactive guidance and requirements collection for SOP creation
- Maintain conversation context across multiple interactions with session management

### Key Features
- **Multi-Agent Collaboration**: Uses CrewAI to orchestrate 4 specialized agents working in sequence
- **Enhanced RAG with Rerank**: Implements cross-encoder reranking for improved document retrieval accuracy
- **Flow Control**: Intelligent workflow that asks clarifying questions (max 2 rounds) before generating SOPs
- **Session Management**: Maintains conversation context across multiple interactions
- **Dual Interface**: Both CLI and Web API interfaces available

### Target Users
- DevOps engineers and SREs working with Kubernetes
- Operations teams needing standardized procedures
- Organizations requiring documented best practices for K8s operations

### Agent Workflow
1. **SOP Requirements Advisor**: Collects detailed requirements (max 2 question rounds)
2. **Query Rewriter**: Optimizes queries for better knowledge retrieval
3. **Knowledge Retriever**: Uses enhanced RAG with rerank to find relevant documentation
4. **Response Synthesizer**: Combines retrieved knowledge with requirements to generate comprehensive SOPs