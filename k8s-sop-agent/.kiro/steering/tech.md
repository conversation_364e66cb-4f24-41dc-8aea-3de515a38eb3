# Technology Stack

## Build System & Package Management
- **Package Manager**: `uv` (Python package manager)
- **Python Version**: 3.10+
- **Project Configuration**: `pyproject.toml`

## Core Framework & Libraries
- **AI Framework**: CrewAI (>=0.30.0) for multi-agent orchestration
- **LLM Integration**: OpenAI-compatible API with custom LLM configuration
- **Web Framework**: FastAPI (>=0.104.0) with Uvicorn server
- **Vector Database**: Qdrant (in-memory) for document storage and retrieval
- **Embeddings**: FastEmbed for document vectorization
- **Reranking**: Sentence Transformers with cross-encoder model (`cross-encoder/ms-marco-MiniLM-L4-v2`)

## Key Dependencies
- **Document Processing**: LangChain ecosystem for RAG pipeline
- **Session Management**: Redis for session storage
- **Authentication**: python-jose for JWT tokens
- **Data Models**: Pydantic for API request/response validation
- **Environment**: python-dotenv for configuration management

## Common Commands

### Development Setup
```bash
# Install dependencies
uv sync

# Install with development dependencies
uv sync --dev

# Activate virtual environment
source .venv/bin/activate
```

### Running the Application
```bash
# CLI Interface (Interactive)
.venv/bin/python src/main.py

# Web API Server
.venv/bin/python src/web_main.py

# Alternative CLI launcher
.venv/bin/python start_chat.py
```

### Testing & Validation
```bash
# Test setup and configuration
.venv/bin/python test_setup.py

# Test rerank functionality
.venv/bin/python test_rerank.py

# Test flow control
.venv/bin/python test_flow_control.py

# Test memory and sessions
.venv/bin/python test_memory.py
```

### Environment Configuration
```bash
# Copy example environment file
cp .env.example .env

# Required environment variables:
# OPENAI_API_KEY - OpenAI API key
# OPENAI_API_BASE - API base URL
# OPENAI_MODEL_NAME - Model name
# SERPER_API_KEY - Optional for web search
```

## Architecture Patterns
- **Multi-Agent Pattern**: Sequential agent execution with context passing
- **RAG with Rerank**: Two-stage retrieval (vector similarity + cross-encoder reranking)
- **Session-Based Memory**: Isolated conversation contexts with persistent storage
- **Flow Control**: Intelligent question-asking with round limits (max 2 rounds)
- **Dual Interface**: Shared business logic between CLI and Web API