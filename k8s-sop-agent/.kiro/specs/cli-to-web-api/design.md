# 设计文档

## 概述

将现有的命令行K8s SOP顾问项目改造为基于FastAPI的Web API服务。该设计保持现有的核心功能（多Agent协作、会话管理、知识库检索），同时提供RESTful API接口，支持HTTP客户端访问。

## 架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   API Gateway   │    │  Core Services  │
│                 │    │                 │    │                 │
│ - Frontend App  │◄──►│ - FastAPI       │◄──►│ - K8sSopCrew    │
│ - Mobile App    │    │ - Authentication│    │ - SessionMgr    │
│ - CLI Client    │    │ - Rate Limiting │    │ - KnowledgeBase │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Data Layer    │
                       │                 │
                       │ - Qdrant Vector │
                       │ - Session Store │
                       │ - File Storage  │
                       └─────────────────┘
```

### 技术栈
- **Web框架**: FastAPI (异步支持，自动API文档)
- **会话存储**: Redis (可选，当前使用内存存储)
- **向量数据库**: Qdrant (现有)
- **认证**: JWT Token 或 API Key
- **文档**: OpenAPI/Swagger (FastAPI自动生成)
- **部署**: Docker + Docker Compose

## 组件和接口

### 1. Web API层 (FastAPI)

#### 核心端点设计
```python
# 主要API端点
POST /api/v1/sop-advice          # 获取SOP建议
POST /api/v1/sessions            # 创建新会话
GET  /api/v1/sessions            # 列出会话
GET  /api/v1/sessions/{id}       # 获取会话详情
DELETE /api/v1/sessions/{id}     # 删除会话
POST /api/v1/sessions/{id}/reset # 重置会话记忆

# 文档管理
POST /api/v1/documents           # 上传SOP文档
GET  /api/v1/documents           # 列出文档
DELETE /api/v1/documents/{id}    # 删除文档

# 系统监控
GET  /health                     # 健康检查
GET  /metrics                    # 系统指标
GET  /docs                       # API文档
```

#### 请求/响应模型
```python
# SOP建议请求
class SOPAdviceRequest(BaseModel):
    question: str
    session_id: Optional[str] = None
    context: Optional[Dict] = None

# SOP建议响应
class SOPAdviceResponse(BaseModel):
    session_id: str
    response: str
    is_question: bool  # 是否为后续询问
    question_round: int
    max_rounds: int
    metadata: Dict

# 会话信息
class SessionInfo(BaseModel):
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int
    status: str
```

### 2. 会话管理层

#### WebSessionManager (扩展现有SessionManager)
```python
class WebSessionManager(SessionManager):
    """Web API专用的会话管理器"""
    
    def __init__(self, storage_backend="memory"):
        # 支持内存和Redis存储
        self.storage_backend = storage_backend
        if storage_backend == "redis":
            self.redis_client = redis.Redis(...)
    
    async def create_session_async(self, session_id: str = None) -> str:
        """异步创建会话"""
        
    async def get_session_async(self, session_id: str) -> Dict:
        """异步获取会话"""
        
    async def cleanup_expired_sessions(self):
        """定期清理过期会话"""
```

### 3. API服务层

#### SOPService
```python
class SOPService:
    """SOP建议服务的业务逻辑层"""
    
    def __init__(self, session_manager: WebSessionManager):
        self.session_manager = session_manager
    
    async def get_sop_advice(self, request: SOPAdviceRequest) -> SOPAdviceResponse:
        """处理SOP建议请求"""
        
    async def handle_multi_round_conversation(self, session_id: str, question: str):
        """处理多轮对话逻辑"""
```

#### DocumentService
```python
class DocumentService:
    """文档管理服务"""
    
    async def upload_document(self, file: UploadFile, collection: str = "dynamic_kb"):
        """上传并处理SOP文档"""
        
    async def list_documents(self) -> List[DocumentInfo]:
        """列出所有文档"""
        
    async def delete_document(self, doc_id: str):
        """删除文档"""
```

### 4. 中间件层

#### 认证中间件
```python
class AuthMiddleware:
    """API认证中间件"""
    
    def __init__(self, auth_type: str = "api_key"):
        self.auth_type = auth_type  # api_key 或 jwt
    
    async def authenticate_request(self, request: Request):
        """验证请求认证"""
```

#### 限流中间件
```python
class RateLimitMiddleware:
    """请求限流中间件"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
```

## 数据模型

### 会话数据模型
```python
class SessionData:
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int
    sop_crew_instance: K8sSopCrew
    metadata: Dict
    status: SessionStatus  # ACTIVE, EXPIRED, DELETED
```

### 文档数据模型
```python
class DocumentMetadata:
    doc_id: str
    filename: str
    collection: str  # static_kb, dynamic_kb
    upload_time: datetime
    file_size: int
    content_hash: str
    status: DocumentStatus  # PROCESSING, READY, ERROR
```

### API响应数据模型
```python
class APIResponse:
    success: bool
    data: Optional[Any]
    error: Optional[str]
    timestamp: datetime
    request_id: str
```

## 错误处理

### 错误分类和处理策略
```python
class APIError(Exception):
    """API基础错误类"""
    def __init__(self, message: str, error_code: str, status_code: int = 500):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code

# 具体错误类型
class SessionNotFoundError(APIError):
    """会话不存在错误"""
    
class AuthenticationError(APIError):
    """认证失败错误"""
    
class RateLimitExceededError(APIError):
    """请求频率超限错误"""
    
class DocumentProcessingError(APIError):
    """文档处理错误"""
```

### 全局错误处理器
```python
@app.exception_handler(APIError)
async def api_error_handler(request: Request, exc: APIError):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "timestamp": datetime.utcnow().isoformat(),
                "request_id": str(uuid.uuid4())
            }
        }
    )
```

## 测试策略

### 单元测试
- **API端点测试**: 使用FastAPI TestClient测试所有端点
- **服务层测试**: 测试业务逻辑和数据处理
- **会话管理测试**: 测试会话创建、管理和清理
- **文档处理测试**: 测试文档上传和知识库更新

### 集成测试
- **端到端API测试**: 完整的请求-响应流程测试
- **多轮对话测试**: 测试会话状态保持和上下文管理
- **并发测试**: 测试多用户同时访问的性能
- **错误场景测试**: 测试各种异常情况的处理

### 性能测试
- **负载测试**: 使用Locust或类似工具测试API性能
- **内存使用测试**: 监控会话和向量存储的内存使用
- **响应时间测试**: 确保API响应时间在可接受范围内

## 部署和配置

### Docker化部署
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "src.web_main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose配置
```yaml
version: '3.8'
services:
  sop-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

### 环境配置
```python
class Settings(BaseSettings):
    # API配置
    api_title: str = "K8s SOP Advisor API"
    api_version: str = "1.0.0"
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    
    # 认证配置
    auth_enabled: bool = True
    api_key_header: str = "X-API-Key"
    jwt_secret_key: str = "your-secret-key"
    
    # 会话配置
    session_timeout_hours: int = 24
    max_sessions_per_user: int = 10
    
    # 限流配置
    rate_limit_requests_per_minute: int = 60
    
    # 存储配置
    session_storage_backend: str = "memory"  # memory, redis
    redis_url: str = "redis://localhost:6379"
    
    class Config:
        env_file = ".env"
```

## 安全考虑

### 认证和授权
- **API Key认证**: 简单的API密钥验证
- **JWT Token**: 支持更复杂的用户认证
- **请求签名**: 可选的请求签名验证

### 数据安全
- **输入验证**: 严格的请求数据验证
- **SQL注入防护**: 使用参数化查询
- **文件上传安全**: 文件类型和大小限制
- **敏感信息脱敏**: 日志中不记录敏感信息

### 网络安全
- **HTTPS强制**: 生产环境强制使用HTTPS
- **CORS配置**: 合理的跨域资源共享配置
- **请求限流**: 防止API滥用
- **IP白名单**: 可选的IP访问控制

## 监控和日志

### 应用监控
- **健康检查端点**: 系统状态监控
- **性能指标**: 响应时间、吞吐量、错误率
- **资源使用**: CPU、内存、磁盘使用情况
- **业务指标**: 会话数量、请求类型分布

### 日志管理
- **结构化日志**: JSON格式的日志输出
- **日志级别**: DEBUG, INFO, WARNING, ERROR
- **请求追踪**: 每个请求的唯一ID追踪
- **敏感信息过滤**: 自动过滤敏感数据

### 告警配置
- **系统告警**: 服务不可用、资源耗尽
- **业务告警**: 错误率过高、响应时间过长
- **安全告警**: 异常访问模式、认证失败