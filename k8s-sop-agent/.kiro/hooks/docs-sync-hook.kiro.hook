{"enabled": true, "name": "Documentation Sync", "description": "Monitors Python source files, configuration files, and project metadata for changes and triggers documentation updates in README.md and docs folder", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.py", "config/*.yaml", "pyproject.toml", "*.md", "src/kb/**/*.md"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this Python project. Please review the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md if there are changes to:\n   - Main functionality or features\n   - Installation or setup instructions\n   - Usage examples or API changes\n   - Project structure or architecture\n\n2. Update any documentation in a /docs folder (if it exists) for:\n   - Technical specifications\n   - API documentation\n   - Architecture diagrams\n   - User guides\n\n3. Pay special attention to changes in:\n   - Core modules (src/main.py, src/crew.py, src/web_main.py)\n   - Configuration files (config/*.yaml, pyproject.toml)\n   - Knowledge base content (src/kb/**/*.md)\n   - API models and tools\n\nEnsure the documentation accurately reflects the current state of the codebase and maintains consistency with the project's multi-agent AI architecture for Kubernetes SOP generation."}}