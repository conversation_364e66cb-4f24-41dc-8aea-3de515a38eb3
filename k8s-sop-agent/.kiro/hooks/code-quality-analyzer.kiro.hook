{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.py", "config/*.yaml", "pyproject.toml", "*.py"]}, "then": {"type": "askAgent", "prompt": "A source code file has been modified. Please analyze the changed code for potential improvements focusing on:\n\n1. **Code Smells**: Identify any code smells like long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check adherence to Python/YAML best practices and conventions\n4. **Readability**: Suggest improvements for code clarity and documentation\n5. **Maintainability**: Identify areas that could be refactored for easier maintenance\n6. **Performance**: Suggest optimizations that don't compromise functionality\n\nConsider the project context:\n- This is a Python project using CrewAI, FastAPI, and AI/ML libraries\n- The codebase follows multi-agent patterns and RAG architecture\n- Configuration is managed through YAML files and environment variables\n\nProvide specific, actionable suggestions while preserving the existing functionality. Focus on improvements that align with the project's architecture and technology stack."}}