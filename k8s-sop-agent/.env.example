# Compatible LLM Configuration (e.g., OpenAI, Groq, etc.)
# Note: The values below are corrected - API_KEY and API_BASE were swapped
OPENAI_API_KEY="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"
OPENAI_API_BASE="https://openrouter.ai/api/v1"
OPENAI_MODEL_NAME="moonshotai/kimi-k2:free"

# Alternative: Using CrewAI's MODEL environment variable (Recommended)
# MODEL=openai/qwen/qwq-32b:free


# Optional: Other LLM configurations
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_API_KEY=your_google_api_key_here
