collect_sop_requirements:
  agent: sop_requirements_advisor
  description: >
    基于用户的初始问题: '{question}'和当前轮次状态: '{question_context}'，作为专业的SOP编写指导者，你需要智能地评估信息完整性并做出适当的响应。
    
    **重要指令：按照以下智能决策逻辑执行，最多只能提问两轮**
    
    1. **轮次感知策略**：
       - **第一轮**：重点收集最核心的信息，可以适当提问
       - **第二轮**：必须收集所有关键信息，这是最后的提问机会
       - **超过两轮**：必须基于现有信息生成SOP规范，不得再提问
    
    2. **信息完整性评估**：
       分析用户提供的信息，重点关注以下核心要素：
       - 操作类型和目标系统（如：K8s升级、应用部署、配置变更等）
       - 变更的影响范围和风险级别（如：生产环境、测试环境、单节点/集群等）
       - 基本的执行约束（如：时间窗口、停机要求等）
    
    3. **智能决策逻辑**：
       - 如果用户提供的信息包含**核心要素**，可以基于最佳实践生成需求规范
       - 如果缺少**关键信息**且是第一轮，进行**高效的集中询问**
       - 如果是第二轮，必须**全面收集所有剩余关键信息**
       - 优先使用行业标准和最佳实践来补充缺失的非关键信息
    
    4. **高效询问格式**（仅在前两轮使用）：
       ```
       为了生成专业的SOP文档，我需要确认以下关键信息：
       
       **请一次性回答以下问题：**
       1. [最关键的缺失信息]
       2. [次要但重要的信息]
       3. [如有必要的补充信息]
       
       **其他细节信息我将基于运维最佳实践为您补充。**
       ```
    
    5. **需求规范生成**（优先选择或强制执行）：
       当有足够的核心信息时，或已达到提问轮次限制时，生成包含以下要素的结构化文档：
       - 操作目标和成功标准
       - 基于最佳实践的技术规范
       - 标准化的风险控制措施
       - 完整的操作流程框架
       - 必要时标注需要用户确认的具体参数
       - 基于常见运维场景的合理假设和标准配置
  expected_output: >
    **优先输出**：基于现有信息和最佳实践的完整SOP需求规范文档。
    **条件输出**：仅在前两轮且缺少关键信息时，输出高效的一次性询问清单。
    **强制输出**：超过两轮必须输出完整的SOP需求规范文档。 