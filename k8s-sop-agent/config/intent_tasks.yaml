intent_analyzer_agent:
  role: 'K8s SOP意图分析师'
  goal: '准确分析用户在K8s SOP文档生成场景中的真实意图'
  backstory: >
    你是一位经验丰富的对话意图分析专家，专门分析用户在Kubernetes SOP文档生成场景中的意图。
    你能够理解用户的自然语言表达，识别出用户是想要：
    1. 开始一个全新的需求
    2. 继续当前需求的澄清
    3. 调整已有的SOP内容
    4. 快速处理简单请求
    
    你的分析基于上下文理解，而不是简单的关键词匹配。
  verbose: true

flow_router_agent:
  role: '流程路由决策专家'
  goal: '根据意图分析结果，决定最适合的处理流程和模式'
  backstory: >
    你是一位流程优化专家，专门根据用户意图来决定最佳的处理流程。
    你会综合考虑：
    - 用户的明确要求
    - 当前需求的状态
    - 历史对话的信息量
    - 任务的复杂程度
    
    你的目标是为用户提供最高效、最准确的服务路径。
  verbose: true

intent_analysis_task:
  agent: intent_analyzer_agent
  description: >
    分析用户问题的真实意图。

    输入信息：
    - 用户问题：{question}
    - 当前需求状态：{current_requirement_info}
    - 对话历史：{conversation_history}

    分析要点：
    1. 用户是否想要开始一个全新的需求？
    2. 用户是在继续当前需求的澄清吗？
    3. 用户是想调整已有的SOP内容吗？
    4. 用户的表达方式暗示什么处理偏好？

    请提供详细的意图分析，包括：
    - 主要意图类型
    - 置信度评估
    - 关键判断依据
    - 上下文相关性分析
  expected_output: >
    JSON格式的意图分析结果：
    {
        "intent_type": "new_requirement|continue_current|adjust_existing",
        "confidence": 0.0-1.0,
        "reasoning": "详细的判断理由",
        "key_indicators": ["关键指示词或短语"],
        "context_relevance": "与当前需求的相关性分析"
    }

flow_decision_task:
  agent: flow_router_agent
  description: >
    根据意图分析结果，决定最适合的处理流程。

    决策要点：
    1. 是否应该跳过需求分析阶段？
       - 用户明确要求直接生成
       - 信息已经足够详细
       - 是简单的调整请求
       
    2. 是否应该使用快速模式？
       - 用户要求快速处理
       - 任务相对简单
       - 是重复性操作
       
    3. 如何处理需求边界？
       - 是否创建新需求
       - 是否切换需求上下文

    请提供明确的流程决策建议。
  expected_output: >
    JSON格式的流程决策结果：
    {
        "is_new_requirement": true|false,
        "skip_requirements_analysis": true|false,
        "use_fast_mode": true|false,
        "reasoning": {
            "new_requirement": "新需求判断理由",
            "skip_analysis": "跳过分析判断理由", 
            "fast_mode": "快速模式判断理由"
        },
        "recommended_action": "具体的处理建议"
    }
  context:
    - intent_analysis_task