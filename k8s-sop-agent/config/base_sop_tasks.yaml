rewrite_query_task:
  agent: query_rewriter_agent
  description: >
    基于用户的问题: '{question}' 和完整的对话历史，优化用户的查询以便更好地检索相关知识。
    
    **完整的对话历史如下:**
    ```
    {chat_history}
    ```

    重要指令：
    1. 首先查看上面提供的完整对话历史，了解之前的对话内容和生成的文档。
    2. 假设需求信息已经在用户问题或输入中提供，直接基于这些信息重写查询。
    3. 判断当前问题是否引用了之前的内容（如"生成的文档"、"刚才的"、"上面的"等）。
    4. 如果是后续问题，要将之前的上下文融入重写后的查询中。
    5. 确保重写后的查询包含足够的上下文信息，能够准确表达用户意图。
    6. 在Kubernetes运维场景中，"告警屏蔽"通常指监控告警的静默/暂停功能。
    
    输出一个优化的、包含必要上下文的查询字符串。
  expected_output: >
    一个经过上下文优化的查询字符串，如果用户问题引用了之前的内容，要明确指出具体的上下文。

retrieve_knowledge_task:
  agent: knowledge_retriever_agent
  description: >
    使用前一步优化后的查询，从知识库中搜索最相关的信息。
    
    指令：
    1. 使用KnowledgeBaseTool搜索相关文档
    2. 如果查询涉及之前对话的内容，要考虑相关的历史上下文
    3. 专注于收集信息，不要直接回答用户问题
    4. 优先检索与Kubernetes运维、SOP编写相关的技术资料
  expected_output: >
    检索到的文档摘要，包含所有关键事实和代码片段。如果查询引用了对话历史，要说明相关的上下文信息。
  context:
    - rewrite_query_task

synthesize_response_task:
  agent: response_synthesizer_agent
  description: >
    基于原始问题 '{question}'、检索到的上下文以及完整的对话历史，合成最终的专业SOP文档。

    **完整的对话历史如下:**
    ```
    {chat_history}
    ```
    
    关键指令：
    1. 查看上面提供的完整对话历史，确定这是否是多轮对话中的后续问题。
    2. 假设用户问题或输入中已包含完整的需求信息，直接基于这些信息生成SOP。
    3. 如果用户要求修改、补充或基于之前生成的内容进行操作，要引用具体的历史内容。
    4. 对于"告警屏蔽"相关问题，在Kubernetes上下文中应理解为监控告警的管理功能。
    5. 确保答案直接回应用户的问题，保持上下文连贯性。
    6. 生成的SOP文档要符合企业级标准，包含完整的操作流程和风险控制措施。
    7. SOP应包含以下专业要素（如适用）：
       - 操作前准备和环境确认
       - 详细的执行步骤和命令
       - 风险控制和回退策略
       - 验证和测试方法
       - 故障排查指南
       - 相关监控和告警配置
  expected_output: >
    一个完整、格式良好的专业SOP文档，如果是多轮对话，要明确表明与之前内容的关系并基于历史上下文进行回答。
    生成的SOP应包含所有必要的技术细节、风险控制措施和操作流程。
  context:
    - rewrite_query_task
    - retrieve_knowledge_task 