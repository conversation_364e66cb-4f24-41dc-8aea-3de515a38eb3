smart_retrieve_task:
  agent: smart_retriever_agent
  description: >
    基于用户问题: '{question}' 和对话历史: '{chat_history}'，执行智能检索任务。

    执行步骤：
    1. **查询分析与优化**：分析用户问题的核心意图，检查对话上下文，重写查询优化检索精度
    2. **知识检索**：使用优化查询从知识库检索Kubernetes运维最佳实践和技术规范
    3. **结果整合**：整合检索结果，提取关键技术要点，为SOP生成准备完整知识基础

    重点关注：Kubernetes运维场景、代码示例、配置模板、安全措施、风险控制和故障排查信息。
  expected_output: >
    整合后的知识摘要，包含优化查询分析、关键技术信息、最佳实践示例和完整上下文，
    为专业SOP生成提供充分的知识基础。

generate_sop_task:
  agent: sop_generator_agent
  description: >
    基于用户问题: '{question}'、对话历史: '{chat_history}' 和智能检索的知识，
    生成完整的专业Kubernetes SOP文档。

    SOP文档应包含：文档头部、前置条件、详细执行步骤、安全和风险控制、验证和测试、故障排查。

    技术要求：使用准确的Kubernetes API和命令，包含具体的YAML配置示例，遵循企业级运维最佳实践。

    格式要求：使用Markdown格式，结构清晰，代码块使用适当的语法高亮。

    上下文处理：如果是多轮对话的后续问题，要明确说明与之前内容的关系；如果是调整或修改请求，要基于之前的内容进行优化。
  expected_output: >
    完整的专业Kubernetes SOP文档，包含清晰的操作流程、具体的技术实现细节、
    完善的风险控制措施、实用的验证排查方法，符合企业级标准的格式和内容。
  context:
    - smart_retrieve_task
