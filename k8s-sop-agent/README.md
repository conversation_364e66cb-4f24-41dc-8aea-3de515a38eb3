# K8s SOP Agent

An AI-powered system for collaboratively writing expert-level Kubernetes Standard Operating Procedures (SOPs) using multi-agent orchestration.

## Features

- **Multi-Agent Collaboration**: Uses CrewAI to orchestrate 4 specialized agents working in sequence
- **Intelligent Flow Control**: Proactive requirements collection with configurable question rounds (max 2)
- **Enhanced RAG with Rerank**: Two-stage retrieval using vector similarity + cross-encoder reranking (`cross-encoder/ms-marco-MiniLM-L4-v2`)
- **Dual Crew Architecture**: Specialized crews for requirements gathering and full SOP generation
- **Session Management**: Isolated conversation contexts with persistent memory storage
- **Dual Interface**: Both interactive CLI and RESTful Web API interfaces
- **Knowledge Base Integration**: Static Kubernetes documentation with dynamic user content support
- **Enhanced Memory Conversation**: 🚧 *In Development* - Advanced content enhancement and intelligent merging

## Architecture

### Dual Crew System

The system uses two specialized crews built with CrewAI:

#### 1. RequirementsCrew
- **Purpose**: Lightweight requirements gathering and analysis
- **Agent**: SOP Requirements Advisor (proactive information collection)
- **Usage**: `RequirementsCrew().crew().kickoff(inputs=inputs)`

#### 2. BaseSopCrew  
- **Purpose**: Complete SOP generation with full 4-agent workflow
- **Agents**: 
  1. **SOP Requirements Advisor**: Collects detailed requirements (max 2 question rounds)
  2. **Query Rewriter**: Optimizes queries for enhanced knowledge retrieval
  3. **Knowledge Retriever**: Uses enhanced RAG with cross-encoder reranking
  4. **Response Synthesizer**: Combines knowledge with requirements for comprehensive SOPs
- **Usage**: `BaseSopCrew().crew().kickoff(inputs=inputs)`

### Current Implementation Status

#### ✅ Fully Implemented
- **CLI Interface**: Interactive session management with multi-turn conversations
- **Dual Crew Architecture**: Simplified one-line crew instantiation and execution
- **Enhanced RAG Pipeline**: Vector search + cross-encoder reranking for improved relevance
- **Session Isolation**: Each conversation maintains separate memory and context
- **Flow Control**: Intelligent question-asking with configurable round limits
- **Basic Web API**: Core endpoints for SOP generation and session management

#### 🚧 In Development
- **Enhanced Memory Conversation**: Advanced content enhancement capabilities
  - ✅ Design and architecture completed
  - 🚧 Core memory management components (src/memory/) - **IN PROGRESS**
  - 🚧 Data models and storage structures
  - 🚧 Intent analysis for enhancement requests
  - 🚧 Intelligent content merging and structure preservation
  - 🚧 Change tracking and content attribution
- **Complete Web API**: Full session management and document upload endpoints
- **Authentication & Security**: API keys, rate limiting, and access control

*See [Enhanced Memory Conversation Specification](.kiro/specs/enhanced-memory-conversation/) and [Implementation Status](IMPLEMENTATION_STATUS.md) for detailed progress. Core memory management components are currently in active development.*

### Enhanced RAG Pipeline

1. **Document Ingestion**: Markdown files → Text chunks → Vector embeddings (FastEmbed)
2. **Vector Retrieval**: Initial similarity search in Qdrant collections (configurable top-k)
3. **Cross-Encoder Rerank**: Uses `cross-encoder/ms-marco-MiniLM-L4-v2` for relevance scoring
4. **Context Synthesis**: Top-ranked documents combined with user requirements for final response

### Knowledge Base Structure
- **Static KB** (`src/kb/static/`): Kubernetes best practices and documentation
- **Dynamic KB** (`src/kb/dynamic/`): User-provided SOPs and runtime content
- **Collections**: Separate vector collections for different content types

## Installation

### Prerequisites

- Python 3.10 or higher
- uv package manager

### Setup

1. Clone or navigate to the project directory:
   ```bash
   cd k8s-sop-agent
   ```

2. Install dependencies using uv:
   ```bash
   uv sync
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

4. Test the setup:
   ```bash
   .venv/bin/python test_setup.py
   ```

## Usage

### Interactive CLI Mode (Recommended)

Run the streamlined interactive interface for conversational SOP creation:

```bash
# 传统Crews模式 (智能会话管理)
uv run python src/main.py

# Flow模式 (状态管理工作流)  
uv run python src/main_flow.py
```

**Key Features:**
- **Intelligent Flow Control**: System automatically determines if more information is needed
- **Dual Crew Processing**: Uses RequirementsCrew for analysis, BaseSopCrew for generation
- **Session Tracking**: Maintains conversation context and question round limits
- **Natural Conversation**: Simply describe your needs in natural language

**No complex commands needed** - just describe your SOP requirements and the system handles the rest!

### 🚧 Enhanced Content Commands (Coming Soon)

```bash
# Content enhancement (natural language) - In Development
"在刚才的SOP中增加..."     # Add content to previous SOP
"补充监控部分"           # Enhance monitoring section
"完善回退策略"           # Complete rollback strategy
"基于之前的文档添加..."   # Add based on previous document
```

### Example Interaction

```bash
📄 K8s SOP文档生成器
==================================================
💬 与AI对话，智能生成Kubernetes SOP文档
🤖 输入您的需求，系统将自动判断是否需要更多信息
==================================================
📁 会话: sop_session_20250124_143022 | 对话: 0 | 提问轮次: 0/2

[1] 您的需求: 我需要写一个Kubernetes部署的SOP

🚀 开始生成SOP文档...
🔍 分析需求中...
📝 需要更多信息 (轮次: 1/2)

============================================================
📋 SOP文档生成结果:
------------------------------------------------------------
为了为您生成专业的SOP文档，我需要了解更多关键信息：

**缺少的关键信息：**
- 具体的操作类型和技术栈
- 操作的影响范围和环境
- 风险控制要求

**请一次性回答以下问题：**
1. 这是什么类型的部署操作？（新部署、滚动更新、蓝绿部署等）
2. 部署的应用类型和规模是什么？
3. 目标环境是什么？（开发、测试、生产）
...
============================================================

[2] 您的需求: 这是一个微服务应用的滚动更新部署，目标环境是生产环境，需要零停机部署

🚀 开始生成SOP文档...
🔍 分析需求中...
✅ 信息充足，直接生成SOP文档
📄 生成SOP文档中...

============================================================
📋 SOP文档生成结果:
------------------------------------------------------------
# Kubernetes微服务滚动更新部署SOP

## 1. 部署前准备
### 1.1 环境验证
- 验证集群状态和资源可用性
- 确认目标命名空间和权限
- 检查依赖服务状态

### 1.2 备份策略
- 备份当前部署配置
- 记录当前版本信息
- 准备回退计划

## 2. 滚动更新执行
### 2.1 更新部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: microservice-app
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
```

### 2.2 执行更新命令
```bash
kubectl set image deployment/microservice-app container-name=new-image:tag
kubectl rollout status deployment/microservice-app --timeout=600s
```

## 3. 监控与验证
- 实时监控Pod状态和健康检查
- 验证服务可用性和响应时间
- 检查日志和错误指标

## 4. 应急处理
### 4.1 回退策略
```bash
kubectl rollout undo deployment/microservice-app
kubectl rollout status deployment/microservice-app
```

### 4.2 故障排查
- 检查Pod事件和日志
- 验证配置和资源限制
- 联系相关团队进行支持
============================================================
```

**Key Features Demonstrated:**
- **Automatic Flow Control**: System detects insufficient information and asks clarifying questions
- **Question Round Tracking**: Shows current round (1/2) and automatically proceeds when information is sufficient
- **Dual Crew Processing**: RequirementsCrew analyzes needs, BaseSopCrew generates comprehensive SOP
- **Session Context**: Maintains conversation history and context across interactions

### Web API Interface

Start the FastAPI web server for HTTP-based access:

```bash
.venv/bin/python src/web_main.py
```

**Current API Status**: ✅ Basic functionality implemented, 🚧 Full features in development

#### Available Endpoints

```bash
# Core SOP Generation (✅ Implemented)
POST /api/v1/sop-advice                      # Generate SOP with session support
GET  /api/v1/sop-advice/status/{session_id} # Get session status and question rounds
POST /api/v1/sop-advice/reset/{session_id}  # Reset session memory

# System Monitoring (✅ Implemented)
GET  /health                                 # Health check
GET  /docs                                   # OpenAPI documentation
GET  /                                       # API information

# Session Management (🚧 In Development)
POST /api/v1/sessions                        # Create new session
GET  /api/v1/sessions                        # List sessions
DELETE /api/v1/sessions/{id}                 # Delete session

# Document Management (🚧 Planned)
POST /api/v1/documents                       # Upload SOP documents
GET  /api/v1/documents                       # List documents
```

#### API Usage Example

See the complete API usage example:

```bash
.venv/bin/python example_web_api_usage.py
```

**Features Demonstrated:**
- Basic SOP generation via REST API
- Session creation and management
- Multi-turn conversation support
- Error handling and validation
- 🚧 Enhanced memory features (simulated for future implementation)

**API Response Format:**
```json
{
  "session_id": "abc123",
  "response": "Generated SOP content...",
  "is_question": false,
  "question_round": 1,
  "max_rounds": 2,
  "metadata": {
    "request_id": "uuid",
    "timestamp": "2025-01-24T14:30:22Z",
    "question_status": "completed"
  }
}
```

### Testing and Validation

Verify your setup and test different components:

```bash
# Basic setup verification
.venv/bin/python test_setup.py

# Test enhanced RAG with rerank model
.venv/bin/python test_rerank.py

# Test web session manager functionality
.venv/bin/python test_web_session_manager.py

# Test flow control and question rounds
.venv/bin/python test_flow_control.py
```

### Direct Crew Usage (Advanced)

For developers who want to use the crews directly:

```python
from src.crews import BaseSopCrew, RequirementsCrew

# Requirements gathering only
requirements_result = RequirementsCrew().crew().kickoff(inputs={
    'question': 'I need a Kubernetes deployment SOP',
    'question_context': 'Initial requirements gathering'
})

# Complete SOP generation
sop_result = BaseSopCrew().crew().kickoff(inputs={
    'question': 'Generate a complete Kubernetes deployment SOP',
    'question_context': 'Full SOP generation with all details'
})
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required: OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Optional: SerperDev API Key for web search
SERPER_API_KEY=your_serper_api_key_here

# Web API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Session Management
SESSION_STORAGE_BACKEND=memory    # memory or redis
SESSION_REDIS_URL=redis://localhost:6379
SESSION_TIMEOUT_HOURS=24
SESSION_MAX_SESSIONS_PER_USER=10

# Enhanced Memory Features (In Development)
ENABLE_ENHANCED_MEMORY=false
MAX_CONVERSATION_HISTORY=100
ENHANCEMENT_CONFIDENCE_THRESHOLD=0.7
```

### Agent Configuration

Agent behaviors are defined in:
- `config/agents.yaml` - Agent roles, goals, and backstories
- `config/tasks.yaml` - Task definitions and expected outputs

### Rerank Configuration

Rerank model settings are configured in:
- `src/config/rerank_config.py` - Rerank model parameters and optimization settings

Key parameters:
```python
RERANK_MODEL_NAME = "cross-encoder/ms-marco-MiniLM-L4-v2"
DEFAULT_TOP_K = 20          # Initial retrieval count
DEFAULT_RERANK_TOP_K = 5    # Final results after rerank
RERANK_MODEL_DEVICE = "cuda" if available else "cpu"
```

### Knowledge Base

The system uses two knowledge bases with enhanced retrieval:
- `src/kb/static/` - Static Kubernetes best practices and documentation
- `src/kb/dynamic/` - User-provided SOPs (populated at runtime)

Both collections support:
- Vector similarity search using FastEmbed
- Cross-encoder reranking for improved relevance
- Configurable retrieval parameters

## Project Structure

```mermain

flowchart TD
    A[启动程序] --> B[初始化会话跟踪器]
    B --> C[用户输入需求]
    C --> D{是否跳过需求分析?}
    D -->|是| F[选择生成模式]
    D -->|否| E[需求收集阶段]
    E --> G{需要更多信息?}
    G -->|是| H{还能提问?}
    H -->|是| I[返回问题给用户]
    H -->|否| F
    G -->|否| F
    I --> C
    F --> J{使用快速模式?}
    J -->|是| K[FastSopCrew生成]
    J -->|否| L[BaseSopCrew生成]
    K --> M[输出SOP文档]
    L --> M
    M --> N{继续对话?}
    N -->|是| C
    N -->|否| O[结束程序]

```


```
k8s-sop-agent/
├── config/                          # Configuration files
│   ├── agents.yaml                  # Agent definitions (roles, goals, backstories)
│   ├── tasks.yaml                   # Task definitions for BaseSopCrew
│   └── requirements_tasks.yaml      # Task definitions for RequirementsCrew
├── src/
│   ├── main.py                      # Streamlined CLI interface
│   ├── web_main.py                  # FastAPI web server
│   ├── crews/                       # CrewAI crew implementations
│   │   ├── __init__.py              # Crew exports
│   │   ├── base_sop_crew.py         # Complete 4-agent SOP generation
│   │   └── requirements_crew.py     # Requirements gathering crew
│   ├── session_manager.py           # ⚠️ DEPRECATED - Legacy session management
│   ├── api/                         # Web API endpoints
│   │   └── sop_endpoints.py         # SOP advice REST API
│   ├── models/                      # Data models
│   │   └── api_models.py            # Pydantic models for API requests/responses
│   ├── config/                      # Configuration modules
│   │   ├── rerank_config.py         # Cross-encoder rerank model settings
│   │   └── session_config.py        # Session management configuration
│   # Note: Memory functionality now uses CrewAI built-in features
│   ├── kb/                          # Knowledge base content
│   │   ├── static/                  # Static Kubernetes documentation
│   │   │   ├── kubernetes-deployment.md
│   │   │   ├── kubernetes-monitoring.md
│   │   │   └── kubernetes-rollback.md
│   │   └── dynamic/                 # Runtime user-provided SOPs
│   │       └── user_sop.md
│   ├── tools/                       # Custom CrewAI tools
│   │   └── kb_tool.py               # Enhanced RAG with cross-encoder reranking
│   └── custom_embedder.py           # Embedding configuration
├── .kiro/                           # Kiro IDE configuration
│   ├── specs/                       # Feature specifications
│   │   ├── cli-to-web-api/          # Web API implementation spec
│   │   └── enhanced-memory-conversation/ # Enhanced memory feature spec
│   └── steering/                    # AI assistant guidance rules
├── docs/                            # Documentation
│   ├── enhanced_memory_conversation.md
│   └── web_session_manager.md
├── memory_storage/                  # Session memory storage
│   └── sop_session_*/               # Individual session directories
├── test_*.py                        # Test scripts for different components
├── example_*.py                     # Usage examples
├── IMPLEMENTATION_STATUS.md         # Current implementation status
├── .env.example                     # Environment variables template
├── pyproject.toml                   # Project dependencies and metadata
└── uv.lock                          # Dependency lock file
```

### Key Architecture Changes

- **Simplified Crew Architecture**: `BaseSopCrew().crew().kickoff()` replaces complex session management
- **Dual Crew System**: Separate crews for requirements gathering vs. full SOP generation  
- **Deprecated Components**: `session_manager.py` marked for removal in favor of direct crew usage
- **Enhanced Memory**: New memory management system in development under `src/memory/`

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running from the project root directory and using the virtual environment:
   ```bash
   source .venv/bin/activate  # or .venv/bin/activate on macOS/Linux
   ```

2. **LLM Configuration Errors**: Verify all required environment variables are set:
   ```bash
   # Required in .env file
   OPENAI_API_KEY=your_key_here
   OPENAI_API_BASE=https://api.your-provider.com/v1
   OPENAI_MODEL_NAME=your_model_name
   ```

3. **Dependency Issues**: Reinstall dependencies if you encounter import errors:
   ```bash
   uv sync --reinstall
   ```

4. **Memory Storage Issues**: If you see memory-related errors, ensure the memory storage directory is writable:
   ```bash
   mkdir -p memory_storage
   chmod 755 memory_storage
   ```

5. **Web API Port Conflicts**: Change the port if 8000 is already in use:
   ```bash
   # In .env file
   API_PORT=8080
   ```

6. **Crew Initialization Failures**: If crews fail to initialize, check your configuration files:
   ```bash
   # Verify config files exist
   ls -la config/agents.yaml config/tasks.yaml config/requirements_tasks.yaml
   ```

### Testing Setup

Run the test scripts to verify everything is working:

```bash
# Basic setup verification (checks imports, config, LLM connection)
.venv/bin/python test_setup.py

# Test the new crew architecture
.venv/bin/python -c "
from src.crews import BaseSopCrew, RequirementsCrew
print('✅ Crews imported successfully')
print('✅ BaseSopCrew:', BaseSopCrew().crew())
print('✅ RequirementsCrew:', RequirementsCrew().crew())
"

# Test enhanced RAG with reranking
.venv/bin/python test_rerank.py

# Test web session manager (if using Web API)
.venv/bin/python test_web_session_manager.py
```

These tests verify:
- Environment configuration and LLM connectivity
- Crew architecture and agent initialization
- Knowledge base and reranking functionality
- Web API components and session management
- Flow control and question round logic

## Implementation Status

### ✅ Production Ready
- **CLI Interface**: Streamlined conversational interface with intelligent flow control
- **Dual Crew Architecture**: `BaseSopCrew` and `RequirementsCrew` with one-line instantiation
- **Enhanced RAG Pipeline**: Vector search + cross-encoder reranking for improved document relevance
- **Flow Control System**: Automatic question-asking with configurable round limits (max 2)
- **Knowledge Base**: Static Kubernetes documentation with vector embeddings
- **Basic Web API**: Core SOP generation endpoints with session support

### 🚧 In Active Development
- **Enhanced Memory Conversation**: Advanced content enhancement capabilities
  - ✅ Design and architecture completed
  - 🚧 Core memory management components
  - 🚧 Intent analysis for enhancement requests
  - 🚧 Intelligent content merging and structure preservation
- **Complete Web API**: Full REST API with authentication
  - ✅ Core SOP endpoints implemented
  - 🚧 Complete session management endpoints
  - 🚧 Document upload and management
  - 🚧 Authentication and rate limiting

### 📋 Planned Features
- **Security & Authentication**: API keys, JWT tokens, rate limiting
- **Advanced Monitoring**: System metrics, performance tracking, usage analytics
- **Deployment**: Docker containerization, production deployment guides
- **Visual Enhancements**: Change tracking, content highlighting, version control

*See [IMPLEMENTATION_STATUS.md](IMPLEMENTATION_STATUS.md) for detailed progress tracking.*

## Current Architecture Details

### Crew Refactoring (January 2025)

The project recently underwent a major architectural refactoring to simplify usage and align with CrewAI best practices:

**Before (Complex)**:
```python
# Old complex instantiation
crew_instance = K8sSopCrew(session_id='test')
result = crew_instance.kickoff_with_flow_control(inputs)
```

**After (Simple)**:
```python
# New one-line instantiation
result = BaseSopCrew().crew().kickoff(inputs=inputs)
result = RequirementsCrew().crew().kickoff(inputs=inputs)
```

### Key Architectural Changes

1. **Simplified Crew Usage**: Direct crew instantiation with `@CrewBase`, `@agent`, `@task`, `@crew` decorators
2. **Dual Crew System**: Separate specialized crews for different use cases
3. **Deprecated Components**: `session_manager.py` marked for removal in favor of direct crew usage
4. **Maintained Compatibility**: CLI and Web API automatically use the new architecture

### Migration Guide

- **CLI Users**: No changes needed - `src/main.py` automatically uses the new architecture
- **Web API Users**: No changes needed - endpoints automatically use new crews
- **Direct Usage**: Replace complex crew instantiation with simple one-line calls
- **Custom Development**: Use the new crew classes as templates for custom implementations

## Development

### Adding New Agents

1. Define the agent in `config/agents.yaml`
2. Add the agent method in the appropriate crew class (`src/crews/base_sop_crew.py` or `src/crews/requirements_crew.py`)
3. Create corresponding tasks in `config/tasks.yaml` or `config/requirements_tasks.yaml`

### Adding New Tools

1. Create a new tool class in `src/tools/`
2. Import and initialize it in the crew class constructor
3. Assign it to the appropriate agents using the `tools` parameter

### Extending Knowledge Base

Add Kubernetes best practices documents to `src/kb/static/` in Markdown format. The system will automatically:
- Process documents into text chunks
- Generate vector embeddings
- Enable cross-encoder reranking for improved retrieval

### Creating New Crews

Follow the pattern in `src/crews/`:
```python
@CrewBase
class YourCrew:
    agents_config = 'path/to/agents.yaml'
    tasks_config = 'path/to/tasks.yaml'
    
    @agent
    def your_agent(self) -> Agent:
        return Agent(config=self.agents_config['agent_name'])
    
    @task  
    def your_task(self) -> Task:
        return Task(config=self.tasks_config['task_name'])
    
    @crew
    def crew(self) -> Crew:
        return Crew(agents=self.agents, tasks=self.tasks)
```

### Enhanced Memory Conversation

For detailed information about the enhanced memory conversation feature, see:
- [Enhanced Memory Conversation Documentation](docs/enhanced_memory_conversation.md)
- [Feature Specification](.kiro/specs/enhanced-memory-conversation/)

### Web API Development

For information about the Web API implementation, see:
- [Web Session Manager Documentation](docs/web_session_manager.md)
- [CLI to Web API Specification](.kiro/specs/cli-to-web-api/)

## Contributing

This project is actively developed and welcomes contributions. Key areas for contribution:

- **Enhanced Memory Conversation**: Help implement advanced content enhancement features
- **Web API Completion**: Complete session management and document upload endpoints  
- **Security Features**: Implement authentication, rate limiting, and access control
- **Testing**: Expand test coverage for all components
- **Documentation**: Improve user guides and API documentation

See [IMPLEMENTATION_STATUS.md](IMPLEMENTATION_STATUS.md) for current development priorities.

## License

This project is provided as-is for educational and development purposes. Please ensure compliance with all third-party dependencies and API terms of service.
