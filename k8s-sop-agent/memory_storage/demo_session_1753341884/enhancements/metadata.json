{"enhancements": [{"enhancement_id": "enh_1753341884", "timestamp": "2025-07-24T15:24:44.652759", "enhancement_type": "add_section", "source_content": "# Kubernetes Pod部署SOP\n\n## 1. 环境准备\n- 确保kubectl已配置并连接到目标集群\n- 验证集群状态：`kubectl cluster-info`\n- 确认目标命名空间存在\n\n## 2. 部署步骤\n1. 准备Pod的YAML配置文件\n2. 验证配置文件语法：`kubectl apply --dry-run=client -f pod.yaml`\n3. 部署Pod：`kubectl apply -f pod.yaml`\n4. 验证部署状态：`kubectl get pods`\n\n## 3. 验证结果\n- 检查Pod状态是否为Running\n- 查看Pod日志：`kubectl logs <pod-name>`\n- 验证Pod网络连通性", "enhanced_content": "# Kubernetes Pod部署SOP v2.0\n\n## 1. 环境准备\n- 确保kubectl已配置并连接到目标集群\n- 验证集群状态：`kubectl cluster-info`\n- 确认目标命名空间存在\n\n## 2. 部署步骤\n1. 准备Pod的YAML配置文件\n2. 验证配置文件语法：`kubectl apply --dry-run=client -f pod.yaml`\n3. 部署Pod：`kubectl apply -f pod.yaml`\n4. 验证部署状态：`kubectl get pods`\n\n## 3. 验证结果\n- 检查Pod状态是否为Running\n- 查看Pod日志：`kubectl logs <pod-name>`\n- 验证Pod网络连通性\n\n## 4. 监控配置\n- 配置Pod资源监控：\n  ```yaml\n  resources:\n    requests:\n      memory: \"64Mi\"\n      cpu: \"250m\"\n    limits:\n      memory: \"128Mi\"\n      cpu: \"500m\"\n  ```\n- 设置健康检查：\n  ```yaml\n  livenessProbe:\n    httpGet:\n      path: /health\n      port: 8080\n    initialDelaySeconds: 30\n    periodSeconds: 10\n  ```\n\n## 5. 故障排查\n### 常见问题诊断\n1. **Pod无法启动**\n   - 检查镜像是否存在：`kubectl describe pod <pod-name>`\n   - 查看事件日志：`kubectl get events`\n   \n2. **Pod状态异常**\n   - 检查资源限制：`kubectl top pod <pod-name>`\n   - 查看详细状态：`kubectl describe pod <pod-name>`\n   \n3. **网络连接问题**\n   - 验证Service配置：`kubectl get svc`\n   - 测试Pod间连通性：`kubectl exec -it <pod-name> -- ping <target-ip>`\n\n### 日志收集\n- 查看实时日志：`kubectl logs -f <pod-name>`\n- 查看历史日志：`kubectl logs <pod-name> --previous`\n- 多容器Pod日志：`kubectl logs <pod-name> -c <container-name>`", "changes_summary": {"added_sections": ["监控配置", "故障排查"], "original_sections": 3, "enhanced_sections": 5, "added_content_length": 801}, "source_hash": "809e1e467c9c3746100e7740ba8b40ea", "enhanced_hash": "99c89f4fa8fe427d870e7e027d47ce7b"}]}