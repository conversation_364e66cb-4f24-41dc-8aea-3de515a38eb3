{"version_id": "doc_1753341884652_809e1e46", "timestamp": "2025-07-24T15:24:44.652175", "content": "# Kubernetes Pod部署SOP\n\n## 1. 环境准备\n- 确保kubectl已配置并连接到目标集群\n- 验证集群状态：`kubectl cluster-info`\n- 确认目标命名空间存在\n\n## 2. 部署步骤\n1. 准备Pod的YAML配置文件\n2. 验证配置文件语法：`kubectl apply --dry-run=client -f pod.yaml`\n3. 部署Pod：`kubectl apply -f pod.yaml`\n4. 验证部署状态：`kubectl get pods`\n\n## 3. 验证结果\n- 检查Pod状态是否为Running\n- 查看Pod日志：`kubectl logs <pod-name>`\n- 验证Pod网络连通性", "doc_type": "sop", "title": "Kubernetes Pod部署SOP v1.0", "tags": [], "parent_version": null, "content_hash": "809e1e467c9c3746100e7740ba8b40ea", "word_count": 44, "char_count": 335}